"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useModal } from "@/components/providers/ModalProvider";
import {
  UsersIcon,
  UserPlusIcon,
  UserCircleIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  BellIcon,
  Cog6ToothIcon,
  ArrowTrendingUpIcon,
  EyeIcon,
} from "@heroicons/react/24/outline";

export default function UserManagementPage() {
  const [stats] = useState({
    totalUsers: 156,
    activeUsers: 142,
    newThisMonth: 12,
    pendingApproval: 3,
  });

  const { openAddUserModal } = useModal();

  const managementOptions = [
    {
      name: "Danh sách người dùng",
      description: "Xem và quản lý tất cả người dùng trong hệ thống",
      href: "/user-management/list",
      icon: ClipboardDocumentListIcon,
      gradient: "from-blue-500 to-blue-600",
      count: stats.totalUsers,
    },
    {
      name: "Thêm người dùng",
      description: "Tạo tài khoản người dùng mới",
      href: "#",
      icon: UserPlusIcon,
      gradient: "from-green-500 to-green-600",
      count: "+",
      onClick: () => openAddUserModal(),
    },
    {
      name: "Hồ sơ người dùng",
      description: "Quản lý thông tin và hồ sơ người dùng",
      href: "/user-management/profiles",
      icon: UserCircleIcon,
      gradient: "from-purple-500 to-purple-600",
      count: stats.activeUsers,
    },
  ];

  const quickStats = [
    {
      name: "Tổng người dùng",
      value: stats.totalUsers,
      change: "+12%",
      changeType: "increase",
      icon: UsersIcon,
      color: "text-blue-600",
    },
    {
      name: "Đang hoạt động",
      value: stats.activeUsers,
      change: "+5%",
      changeType: "increase",
      icon: ArrowTrendingUpIcon,
      color: "text-green-600",
    },
    {
      name: "Mới tháng này",
      value: stats.newThisMonth,
      change: "+8%",
      changeType: "increase",
      icon: UserPlusIcon,
      color: "text-purple-600",
    },
    {
      name: "Chờ duyệt",
      value: stats.pendingApproval,
      change: "-2",
      changeType: "decrease",
      icon: BellIcon,
      color: "text-orange-600",
    },
  ];

  const recentActivities = [
    {
      user: "Nguyễn Văn A",
      action: "Đăng nhập",
      time: "2 phút trước",
      status: "success",
    },
    {
      user: "Trần Thị B",
      action: "Cập nhật hồ sơ",
      time: "15 phút trước",
      status: "info",
    },
    {
      user: "Lê Văn C",
      action: "Đăng ký tài khoản",
      time: "1 giờ trước",
      status: "pending",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center mb-2">
                <div className="p-2 bg-blue-100 rounded-lg mr-3">
                  <UsersIcon className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Quản lý người dùng
                  </h1>
                  <p className="text-gray-600 mt-1">
                    Quản lý tài khoản, thông tin cá nhân và quyền truy cập
                  </p>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <Cog6ToothIcon className="h-4 w-4 mr-2" />
                Cài đặt
              </button>
              <button
                onClick={openAddUserModal}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
              >
                <UserPlusIcon className="h-4 w-4 mr-2" />
                Thêm người dùng
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat) => (
            <div
              key={stat.name}
              className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    {stat.name}
                  </p>
                  <p className="text-3xl font-bold text-gray-900 mt-2">
                    {stat.value}
                  </p>
                  <div className="flex items-center mt-2">
                    <span
                      className={`text-sm font-medium ${
                        stat.changeType === "increase"
                          ? "text-green-600"
                          : "text-red-600"
                      }`}
                    >
                      {stat.change}
                    </span>
                    <span className="text-sm text-gray-500 ml-1">
                      so với tháng trước
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg bg-gray-50`}>
                  <stat.icon className={`h-6 w-6 ${stat.color}`} />
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Management Options */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Chức năng quản lý
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {managementOptions.map((option) => {
                if (option.onClick) {
                  return (
                    <button
                      key={option.name}
                      onClick={option.onClick}
                      type="button"
                      className="group relative bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 overflow-hidden text-left w-full"
                    >
                      <div
                        className={`absolute inset-0 bg-gradient-to-br ${option.gradient} opacity-0 group-hover:opacity-5 transition-opacity`}
                      />

                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div
                            className={`p-3 rounded-lg bg-gradient-to-br ${option.gradient} text-white`}
                          >
                            <option.icon className="h-6 w-6" />
                          </div>
                          <div className="text-right">
                            <span className="text-2xl font-bold text-gray-900">
                              {option.count}
                            </span>
                          </div>
                        </div>

                        <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                          {option.name}
                        </h3>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {option.description}
                        </p>

                        <div className="mt-4 flex items-center text-blue-600 text-sm font-medium">
                          <span>Xem chi tiết</span>
                          <EyeIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </button>
                  );
                } else {
                  return (
                    <Link
                      key={option.name}
                      href={option.href}
                      className="group relative bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-lg transition-all duration-300 overflow-hidden text-left w-full"
                    >
                      <div
                        className={`absolute inset-0 bg-gradient-to-br ${option.gradient} opacity-0 group-hover:opacity-5 transition-opacity`}
                      />

                      <div className="relative">
                        <div className="flex items-center justify-between mb-4">
                          <div
                            className={`p-3 rounded-lg bg-gradient-to-br ${option.gradient} text-white`}
                          >
                            <option.icon className="h-6 w-6" />
                          </div>
                          <div className="text-right">
                            <span className="text-2xl font-bold text-gray-900">
                              {option.count}
                            </span>
                          </div>
                        </div>

                        <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                          {option.name}
                        </h3>
                        <p className="text-gray-600 text-sm leading-relaxed">
                          {option.description}
                        </p>

                        <div className="mt-4 flex items-center text-blue-600 text-sm font-medium">
                          <span>Xem chi tiết</span>
                          <EyeIcon className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                        </div>
                      </div>
                    </Link>
                  );
                }
              })}
            </div>
          </div>

          {/* Recent Activities */}
          <div className="lg:col-span-1">
            <h2 className="text-xl font-semibold text-gray-900 mb-6">
              Hoạt động gần đây
            </h2>
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div
                      className={`w-2 h-2 rounded-full ${
                        activity.status === "success"
                          ? "bg-green-500"
                          : activity.status === "info"
                          ? "bg-blue-500"
                          : "bg-yellow-500"
                      }`}
                    />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {activity.user}
                      </p>
                      <p className="text-sm text-gray-500">{activity.action}</p>
                    </div>
                    <div className="text-xs text-gray-400">{activity.time}</div>
                  </div>
                ))}
              </div>

              <div className="mt-6 pt-4 border-t border-gray-200">
                <Link
                  href="/user-management/list"
                  className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  Xem tất cả hoạt động →
                </Link>
              </div>
            </div>

            {/* Security Notice */}
            <div className="mt-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
              <div className="flex items-start space-x-3">
                <ShieldCheckIcon className="h-6 w-6 text-blue-600 mt-1" />
                <div>
                  <h3 className="text-sm font-semibold text-blue-900 mb-2">
                    Bảo mật & Quyền riêng tư
                  </h3>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Dữ liệu được mã hóa end-to-end</li>
                    <li>• Audit log cho mọi thay đổi</li>
                    <li>• Tuân thủ GDPR và CCPA</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
