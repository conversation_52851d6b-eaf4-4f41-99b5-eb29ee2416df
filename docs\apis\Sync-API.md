# Sync Management API - Quản lý Đồng bộ

## Tổng quan
API quản lý đồng bộ cho phép đồng bộ files/folders với Google Drive và các cloud storage khác.

**Base URL:** `/api/v1/sync`

---

## 1. <PERSON><PERSON><PERSON> hoạt đồng bộ Google Drive

### `POST /api/v1/sync/google-drive/trigger`

Kích hoạt quá trình đồng bộ với Google Drive.

#### Request Body

```json
{
  "syncType": "full",
  "folders": [
    "folder123-456-789",
    "folder456-789-012"
  ],
  "options": {
    "overwriteLocal": false,
    "deleteOrphaned": false,
    "syncMetadata": true
  }
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `syncType` | string | Có | Loại đồng bộ: `full`, `incremental`, `upload-only`, `download-only` |
| `folders` | UUID[] | Không | Danh sách folder cần đồng bộ (rỗng = tất cả) |
| `options.overwriteLocal` | boolean | Không | Ghi đè files local nếu Google Drive mới hơn |
| `options.deleteOrphaned` | boolean | Không | Xóa files không còn trên Google Drive |
| `options.syncMetadata` | boolean | Không | Đồng bộ metadata (tags, descriptions) |

#### Response Body

**Thành công:**
```json
{
  "success": true,
  "message": "Google Drive sync started successfully",
  "data": {
    "syncJobId": "sync-job-123-456",
    "syncType": "full",
    "status": "starting",
    "startedAt": "2023-03-22T16:00:00Z",
    "estimatedDuration": "15-30 minutes",
    "foldersToSync": 2,
    "progressUrl": "/api/v1/sync/status/sync-job-123-456"
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `202 Accepted`: Sync job đã được tạo và bắt đầu

**❌ Lỗi:**
- `400 Bad Request`: Tham số không hợp lệ
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Chưa kết nối Google Drive
- `409 Conflict`: Đã có sync job đang chạy
- `503 Service Unavailable`: Google Drive service không khả dụng

#### Ví dụ sử dụng

```bash
# Đồng bộ toàn bộ
POST /api/v1/sync/google-drive/trigger
{
  "syncType": "full"
}

# Đồng bộ incremental cho folders cụ thể
POST /api/v1/sync/google-drive/trigger
{
  "syncType": "incremental",
  "folders": ["folder123", "folder456"],
  "options": {
    "syncMetadata": true
  }
}

# Chỉ upload lên Google Drive
POST /api/v1/sync/google-drive/trigger
{
  "syncType": "upload-only",
  "options": {
    "overwriteLocal": false
  }
}
```

---

## 2. Lấy trạng thái đồng bộ

### `GET /api/v1/sync/status`

Lấy trạng thái tổng quan của tất cả quá trình đồng bộ.

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `provider` | string | Không | Lọc theo provider: `google-drive`, `r2` |
| `status` | string | Không | Lọc theo trạng thái: `running`, `completed`, `failed` |
| `limit` | integer | Không | Số lượng jobs trả về (mặc định: 10) |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "currentSync": {
      "jobId": "sync-job-123-456",
      "provider": "google-drive",
      "syncType": "full",
      "status": "running",
      "progress": {
        "percentage": 35.5,
        "filesProcessed": 142,
        "totalFiles": 400,
        "currentFile": "documents/report-march.pdf",
        "bytesTransferred": 52428800,
        "totalBytes": 147783680,
        "formattedTransferred": "50 MB",
        "formattedTotal": "141 MB"
      },
      "startedAt": "2023-03-22T16:00:00Z",
      "estimatedCompletion": "2023-03-22T16:25:00Z",
      "errors": [],
      "warnings": [
        {
          "file": "old-document.pdf",
          "message": "File modified during sync",
          "timestamp": "2023-03-22T16:15:00Z"
        }
      ]
    },
    "recentJobs": [
      {
        "jobId": "sync-job-789-012",
        "provider": "google-drive",
        "syncType": "incremental",
        "status": "completed",
        "startedAt": "2023-03-22T14:00:00Z",
        "completedAt": "2023-03-22T14:08:00Z",
        "duration": "8 minutes",
        "results": {
          "filesProcessed": 25,
          "filesUploaded": 3,
          "filesDownloaded": 1,
          "filesUpdated": 2,
          "filesDeleted": 0,
          "errors": 0,
          "warnings": 1
        }
      }
    ],
    "summary": {
      "lastFullSync": "2023-03-21T10:00:00Z",
      "lastIncrementalSync": "2023-03-22T14:00:00Z",
      "totalFilesInSync": 1247,
      "pendingUploads": 5,
      "pendingDownloads": 2,
      "conflictedFiles": 0,
      "nextScheduledSync": "2023-03-23T02:00:00Z"
    }
  }
}
```

#### Ví dụ sử dụng

```bash
# Lấy trạng thái tổng quan
GET /api/v1/sync/status

# Chỉ lấy Google Drive jobs
GET /api/v1/sync/status?provider=google-drive

# Lấy jobs đang chạy
GET /api/v1/sync/status?status=running

# Lấy 5 jobs gần nhất
GET /api/v1/sync/status?limit=5
```

---

## 3. Lấy chi tiết sync job

### `GET /api/v1/sync/status/{jobId}`

Lấy thông tin chi tiết của một sync job cụ thể.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `jobId` | UUID | Có | ID của sync job |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "jobId": "sync-job-123-456",
    "provider": "google-drive",
    "syncType": "full",
    "status": "running",
    "userId": "user123-456-789",
    "startedAt": "2023-03-22T16:00:00Z",
    "estimatedCompletion": "2023-03-22T16:25:00Z",
    "completedAt": null,
    "configuration": {
      "folders": ["folder123", "folder456"],
      "overwriteLocal": false,
      "deleteOrphaned": false,
      "syncMetadata": true
    },
    "progress": {
      "percentage": 35.5,
      "phase": "uploading",
      "filesProcessed": 142,
      "totalFiles": 400,
      "currentFile": "documents/report-march.pdf",
      "bytesTransferred": 52428800,
      "totalBytes": 147783680,
      "speed": "2.5 MB/s",
      "eta": "15 minutes"
    },
    "statistics": {
      "filesUploaded": 45,
      "filesDownloaded": 12,
      "filesUpdated": 8,
      "filesSkipped": 77,
      "filesDeleted": 0,
      "bytesUploaded": 31457280,
      "bytesDownloaded": 20971520,
      "conflicts": 0
    },
    "errors": [],
    "warnings": [
      {
        "file": "old-document.pdf",
        "message": "File modified during sync",
        "timestamp": "2023-03-22T16:15:00Z",
        "severity": "low"
      }
    ],
    "logs": [
      {
        "timestamp": "2023-03-22T16:00:00Z",
        "level": "info",
        "message": "Sync job started",
        "details": {}
      },
      {
        "timestamp": "2023-03-22T16:05:00Z",
        "level": "info",
        "message": "Processing folder: Documents",
        "details": {
          "folderId": "folder123",
          "itemCount": 150
        }
      }
    ]
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Lấy thông tin thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền xem job này
- `404 Not Found`: Job không tồn tại

---

## 4. Hủy sync job

### `DELETE /api/v1/sync/jobs/{jobId}`

Hủy một sync job đang chạy.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `jobId` | UUID | Có | ID của sync job |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `reason` | string | Không | Lý do hủy job |

#### Response Body

```json
{
  "success": true,
  "message": "Sync job cancelled successfully",
  "data": {
    "jobId": "sync-job-123-456",
    "status": "cancelled",
    "cancelledAt": "2023-03-22T16:20:00Z",
    "reason": "User requested cancellation",
    "progress": {
      "percentage": 35.5,
      "filesProcessed": 142,
      "totalFiles": 400
    },
    "partialResults": {
      "filesUploaded": 45,
      "filesDownloaded": 12,
      "filesUpdated": 8
    }
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Hủy job thành công

**❌ Lỗi:**
- `400 Bad Request`: Job không thể hủy (đã hoàn thành hoặc thất bại)
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền hủy job này
- `404 Not Found`: Job không tồn tại

---

## 5. Di chuyển file lên Google Drive

### `POST /api/v1/sync/google-drive/move-file`

Di chuyển file từ R2 storage lên Google Drive.

#### Request Body

```json
{
  "fileId": "file123-456-789",
  "targetFolderId": "google-drive-folder-id",
  "options": {
    "deleteFromR2": false,
    "updateMetadata": true,
    "createFolder": true
  }
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `fileId` | UUID | Có | ID của file cần di chuyển |
| `targetFolderId` | string | Không | ID folder trên Google Drive |
| `options.deleteFromR2` | boolean | Không | Xóa file khỏi R2 sau khi upload |
| `options.updateMetadata` | boolean | Không | Cập nhật metadata trên Google Drive |
| `options.createFolder` | boolean | Không | Tạo folder nếu không tồn tại |

#### Response Body

```json
{
  "success": true,
  "message": "File moved to Google Drive successfully",
  "data": {
    "fileId": "file123-456-789",
    "fileName": "report-march.pdf",
    "googleDriveId": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "googleDriveUrl": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
    "uploadedAt": "2023-03-22T16:30:00Z",
    "fileSize": 2048576,
    "storageProvider": "GoogleDrive",
    "r2Status": "preserved"
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Di chuyển thành công

**❌ Lỗi:**
- `400 Bad Request`: File không hợp lệ, đã tồn tại trên Google Drive
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền truy cập file hoặc Google Drive
- `404 Not Found`: File không tồn tại
- `413 Payload Too Large`: File quá lớn cho Google Drive
- `507 Insufficient Storage`: Không đủ dung lượng Google Drive

---

## 6. Lấy danh sách Google Drive files

### `GET /api/v1/sync/google-drive/files`

Lấy danh sách files từ Google Drive của user.

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mặc định | Mô tả |
|---------|------|----------|----------|-------|
| `page` | integer | Không | 1 | Số trang |
| `pageSize` | integer | Không | 20 | Số items/trang |
| `folderId` | string | Không | - | ID folder trên Google Drive |
| `searchTerm` | string | Không | - | Tìm kiếm theo tên file |
| `mimeType` | string | Không | - | Lọc theo MIME type |
| `sortBy` | string | Không | `name` | Sắp xếp: `name`, `size`, `modifiedTime` |
| `sortOrder` | string | Không | `asc` | Thứ tự: `asc`, `desc` |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "files": [
      {
        "id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "name": "report-march.pdf",
        "mimeType": "application/pdf",
        "size": 2048576,
        "formattedSize": "2 MB",
        "createdTime": "2023-03-22T16:30:00Z",
        "modifiedTime": "2023-03-22T16:30:00Z",
        "webViewLink": "https://drive.google.com/file/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/view",
        "webContentLink": "https://drive.google.com/uc?id=1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
        "thumbnailLink": "https://lh3.googleusercontent.com/...",
        "owners": [
          {
            "displayName": "Nguyễn Văn A",
            "emailAddress": "<EMAIL>"
          }
        ],
        "permissions": [
          {
            "type": "user",
            "role": "owner",
            "emailAddress": "<EMAIL>"
          }
        ],
        "syncStatus": {
          "isSynced": true,
          "localFileId": "file123-456-789",
          "lastSyncAt": "2023-03-22T16:30:00Z",
          "syncDirection": "upload"
        }
      }
    ],
    "totalCount": 156,
    "page": 1,
    "pageSize": 20,
    "totalPages": 8,
    "folderInfo": {
      "id": "root",
      "name": "My Drive",
      "path": "/"
    }
  }
}
```

#### Ví dụ sử dụng

```bash
# Lấy files trong root
GET /api/v1/sync/google-drive/files

# Files trong folder cụ thể
GET /api/v1/sync/google-drive/files?folderId=1ABC123xyz

# Tìm kiếm PDF files
GET /api/v1/sync/google-drive/files?searchTerm=report&mimeType=application/pdf

# Sắp xếp theo kích thước
GET /api/v1/sync/google-drive/files?sortBy=size&sortOrder=desc
```

---

## 7. Cấu hình đồng bộ tự động

### `PUT /api/v1/sync/settings`

Cập nhật cài đặt đồng bộ tự động.

#### Request Body

```json
{
  "googleDrive": {
    "enabled": true,
    "autoSync": true,
    "syncInterval": "6h",
    "syncType": "incremental",
    "uploadNewFiles": true,
    "downloadUpdates": true,
    "conflictResolution": "keep-both",
    "excludePatterns": [
      "*.tmp",
      "*.log",
      ".DS_Store"
    ]
  },
  "notifications": {
    "onComplete": true,
    "onError": true,
    "onConflict": true,
    "email": "<EMAIL>"
  }
}
```

| Trường | Kiểu | Mô tả |
|--------|------|-------|
| `googleDrive.enabled` | boolean | Bật/tắt Google Drive sync |
| `googleDrive.autoSync` | boolean | Đồng bộ tự động |
| `googleDrive.syncInterval` | string | Khoảng thời gian sync: `1h`, `6h`, `12h`, `24h` |
| `googleDrive.syncType` | string | Loại sync mặc định: `full`, `incremental` |
| `googleDrive.uploadNewFiles` | boolean | Tự động upload files mới |
| `googleDrive.downloadUpdates` | boolean | Tự động download cập nhật |
| `googleDrive.conflictResolution` | string | Xử lý xung đột: `keep-local`, `keep-remote`, `keep-both` |
| `googleDrive.excludePatterns` | string[] | Patterns files bỏ qua |
| `notifications.*` | boolean | Bật/tắt thông báo |

#### Response Body

```json
{
  "success": true,
  "message": "Sync settings updated successfully",
  "data": {
    "googleDrive": {
      "enabled": true,
      "autoSync": true,
      "syncInterval": "6h",
      "nextScheduledSync": "2023-03-22T22:00:00Z"
    },
    "notifications": {
      "onComplete": true,
      "onError": true,
      "onConflict": true
    },
    "updatedAt": "2023-03-22T16:45:00Z"
  }
}
```

---

## 8. Lấy cài đặt đồng bộ

### `GET /api/v1/sync/settings`

Lấy cài đặt đồng bộ hiện tại của user.

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "googleDrive": {
      "enabled": true,
      "connected": true,
      "accountEmail": "<EMAIL>",
      "autoSync": true,
      "syncInterval": "6h",
      "syncType": "incremental",
      "lastSync": "2023-03-22T14:00:00Z",
      "nextScheduledSync": "2023-03-22T22:00:00Z",
      "uploadNewFiles": true,
      "downloadUpdates": true,
      "conflictResolution": "keep-both",
      "excludePatterns": ["*.tmp", "*.log"],
      "quota": {
        "used": **********,
        "total": ***********,
        "formattedUsed": "5 GB",
        "formattedTotal": "16 GB",
        "percentage": 31.25
      }
    },
    "notifications": {
      "onComplete": true,
      "onError": true,
      "onConflict": true,
      "email": "<EMAIL>"
    },
    "statistics": {
      "totalSyncs": 45,
      "successfulSyncs": 43,
      "failedSyncs": 2,
      "lastSuccessfulSync": "2023-03-22T14:00:00Z",
      "totalFilesSynced": 1247,
      "totalBytesTransferred": **********
    }
  }
}
```

---

## Workflow sử dụng

### 1. Thiết lập đồng bộ
```
1. GET /sync/settings → Kiểm tra cài đặt hiện tại
2. PUT /sync/settings → Cấu hình đồng bộ
3. POST /sync/google-drive/trigger → Kích hoạt sync đầu tiên
```

### 2. Theo dõi đồng bộ
```
1. GET /sync/status → Xem trạng thái tổng quan
2. GET /sync/status/{jobId} → Chi tiết sync job
3. DELETE /sync/jobs/{jobId} → Hủy job nếu cần
```

### 3. Quản lý files Google Drive
```
1. GET /sync/google-drive/files → Xem files trên Drive
2. POST /sync/google-drive/move-file → Di chuyển file lên Drive
3. GET /sync/status → Kiểm tra trạng thái sync
```

## Error Codes tổng hợp

| Code | HTTP Status | Mô tả |
|------|-------------|-------|
| `SYNC_JOB_NOT_FOUND` | 404 | Sync job không tồn tại |
| `SYNC_ALREADY_RUNNING` | 409 | Đã có sync job đang chạy |
| `GOOGLE_DRIVE_NOT_CONNECTED` | 403 | Chưa kết nối Google Drive |
| `GOOGLE_DRIVE_QUOTA_EXCEEDED` | 507 | Hết dung lượng Google Drive |
| `GOOGLE_DRIVE_API_ERROR` | 502 | Lỗi Google Drive API |
| `FILE_ALREADY_ON_DRIVE` | 409 | File đã tồn tại trên Google Drive |
| `SYNC_CANCELLED` | 410 | Sync job đã bị hủy |
| `INVALID_SYNC_TYPE` | 400 | Loại sync không hợp lệ |

## Sync Types

- **full**: Đồng bộ toàn bộ files và folders
- **incremental**: Chỉ đồng bộ thay đổi từ lần sync cuối
- **upload-only**: Chỉ upload lên Google Drive
- **download-only**: Chỉ download từ Google Drive

## Conflict Resolution

- **keep-local**: Giữ phiên bản local khi có xung đột
- **keep-remote**: Giữ phiên bản Google Drive
- **keep-both**: Giữ cả hai, đổi tên file xung đột
