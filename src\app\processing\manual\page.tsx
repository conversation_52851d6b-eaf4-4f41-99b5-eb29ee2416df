"use client";

import React, { useState, useCallback, useEffect } from "react";
import {
  PlayIcon,
  DocumentIcon,
  EyeIcon,
  ArrowUpTrayIcon,
  ScissorsIcon,
  PlusIcon,
  MinusIcon,
  CheckCircleIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassPlusIcon,
  MagnifyingGlassMinusIcon,
  ArrowsPointingOutIcon,
  ArrowsPointingInIcon,
} from "@heroicons/react/24/outline";
import { useDropzone } from "react-dropzone";

interface FileItem {
  id: string;
  name: string;
  size: string;
  uploadDate: Date;
  status: "ready" | "processing" | "completed" | "error";
  pageCount?: number;
}

interface SplitRange {
  id: string;
  name: string;
  startPage: number;
  endPage: number;
}

type SplitMode = "by-pages" | "custom";

export default function ManualDetachPage() {
  const [selectedFile, setSelectedFile] = useState<string>("1"); // Pre-select VINCOM file
  const [files, setFiles] = useState<FileItem[]>([
    {
      id: "1",
      name: "Instant_Company_Report_VINCOM_RETAIL_OPERATION_COMPANY_LIMITED.pdf",
      size: "4.2 MB",
      uploadDate: new Date(Date.now() - 86400000),
      status: "ready",
      pageCount: 15,
    },
    {
      id: "2",
      name: "Contract_Document.pdf",
      size: "1.8 MB",
      uploadDate: new Date(Date.now() - *********),
      status: "completed",
      pageCount: 8,
    },
    {
      id: "3",
      name: "Invoice_2024_001.pdf",
      size: "2.4 MB",
      uploadDate: new Date(Date.now() - *********),
      status: "ready",
      pageCount: 5,
    },
  ]);

  const [isUploading, setIsUploading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const [previewFile, setPreviewFile] = useState<{
    name: string;
    url: string;
  } | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(100);

  const [splitMode, setSplitMode] = useState<SplitMode>("by-pages");
  const [pagesPerSplit, setPagesPerSplit] = useState(1);
  const [customRanges, setCustomRanges] = useState<SplitRange[]>([
    { id: "1", name: "Phần 1", startPage: 1, endPage: 3 },
    { id: "2", name: "Phần 2", startPage: 4, endPage: 8 },
    { id: "3", name: "Phần 3", startPage: 9, endPage: 15 },
  ]);
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Helper function to format date consistently
  const formatDate = (date: Date) => {
    if (!isMounted) return ""; // Return empty string during SSR
    return (
      date.toLocaleDateString("vi-VN") +
      " " +
      date.toLocaleTimeString("vi-VN", {
        hour: "2-digit",
        minute: "2-digit",
      })
    );
  };

  const selectedFileData = files.find((f) => f.id === selectedFile);

  // Upload files using dropzone
  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      setIsUploading(true);

      for (const file of acceptedFiles) {
        // Simulate getting page count (in real app, this would come from backend)
        const mockPageCount = Math.floor(Math.random() * 20) + 5; // Random 5-25 pages

        const newFile: FileItem = {
          id: Date.now().toString() + Math.random(),
          name: file.name,
          size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
          uploadDate: new Date(),
          status: "ready",
          pageCount: mockPageCount,
        };

        setFiles((prev) => [newFile, ...prev]);

        // Auto-select the first uploaded file
        if (!selectedFile) {
          setSelectedFile(newFile.id);
          setCustomRanges([
            {
              id: "1",
              name: "Phần 1",
              startPage: 1,
              endPage: Math.min(2, mockPageCount),
            },
          ]);
        }
      }

      setIsUploading(false);
    },
    [selectedFile]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "application/pdf": [".pdf"],
    },
    multiple: true,
  });

  const handleFileSelect = (fileId: string) => {
    setSelectedFile(fileId);
    const fileData = files.find((f) => f.id === fileId);
    // Reset ranges when selecting new file
    setCustomRanges([
      {
        id: "1",
        name: "Phần 1",
        startPage: 1,
        endPage: Math.min(2, fileData?.pageCount || 2),
      },
    ]);
  };

  const addCustomRange = () => {
    const newId = (customRanges.length + 1).toString();
    const lastRange = customRanges[customRanges.length - 1];
    const startPage = lastRange ? lastRange.endPage + 1 : 1;
    const maxPage = selectedFileData?.pageCount || 10;

    setCustomRanges((prev) => [
      ...prev,
      {
        id: newId,
        name: `Phần ${newId}`,
        startPage: Math.min(startPage, maxPage),
        endPage: Math.min(startPage + 1, maxPage),
      },
    ]);
  };

  const removeCustomRange = (id: string) => {
    setCustomRanges((prev) => prev.filter((range) => range.id !== id));
  };

  const updateCustomRange = (
    id: string,
    field: "name" | "startPage" | "endPage",
    value: string | number
  ) => {
    setCustomRanges((prev) =>
      prev.map((range) =>
        range.id === id ? { ...range, [field]: value } : range
      )
    );
  };

  const handleStartProcessing = () => {
    if (!selectedFile) return;

    setIsProcessing(true);

    // Update file status to processing
    setFiles((prev) =>
      prev.map((file) =>
        file.id === selectedFile ? { ...file, status: "processing" } : file
      )
    );

    // Simulate processing
    setTimeout(() => {
      setFiles((prev) =>
        prev.map((file) =>
          file.id === selectedFile ? { ...file, status: "completed" } : file
        )
      );
      setIsProcessing(false);
    }, 3000);
  };

  const getStatusColor = (status: FileItem["status"]) => {
    switch (status) {
      case "ready":
        return "bg-gray-100 text-gray-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      case "error":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: FileItem["status"]) => {
    switch (status) {
      case "processing":
        return (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        );
      case "completed":
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case "error":
        return <XMarkIcon className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const handlePreview = (fileName: string) => {
    // For demo purposes, we'll use a sample PDF URL
    // In real app, this would be the actual file URL
    const pdfUrl =
      "/Instant_Company_Report_VINCOM_RETAIL_OPERATION_COMPANY_LIMITED.pdf";
    setPreviewFile({
      name: fileName,
      url: pdfUrl,
    });
  };

  const closePreview = () => {
    setPreviewFile(null);
    setIsFullscreen(false);
    setCurrentPage(1);
    setTotalPages(1);
    setZoomLevel(100);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const zoomIn = () => {
    setZoomLevel((prev) => Math.min(prev + 25, 200));
  };

  const zoomOut = () => {
    setZoomLevel((prev) => Math.max(prev - 25, 50));
  };

  const nextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const prevPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleDownload = (fileName: string) => {
    console.log(`Downloading: ${fileName}`);
    alert(`Tải xuống: ${fileName}`);
  };

  return (
    <div className="py-6 space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <PlayIcon className="h-8 w-8 text-green-600 mr-3" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Manual Detach Processing
              </h1>
              <p className="text-gray-600">
                Tách PDF theo số trang hoặc tùy chọn của bạn
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Upload Zone */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Tải PDF lên để tách thủ công
        </h2>
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive
              ? "border-green-500 bg-green-50"
              : "border-gray-300 hover:border-gray-400"
          } ${isUploading ? "opacity-50 cursor-not-allowed" : ""}`}
        >
          <input {...getInputProps()} disabled={isUploading} />
          <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">
            {isDragActive
              ? "Thả file PDF vào đây..."
              : "Kéo thả file PDF hoặc click để chọn"}
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Hỗ trợ file PDF, tối đa 50MB
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* File Selection */}
        <div className="lg:col-span-2 space-y-6">
          {/* File List */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">
                  Chọn file để tách
                </h2>
                <span className="text-sm text-gray-500">
                  {files.length} file
                </span>
              </div>
            </div>
            <div className="divide-y divide-gray-200 max-h-96 overflow-y-auto">
              {files.map((file) => (
                <div key={file.id} className="p-6">
                  <div className="flex items-center space-x-4">
                    <input
                      type="radio"
                      name="selectedFile"
                      checked={selectedFile === file.id}
                      onChange={() => handleFileSelect(file.id)}
                      disabled={file.status === "processing"}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 disabled:opacity-50"
                    />
                    <DocumentIcon className="h-8 w-8 text-gray-400" />
                    <div className="flex-1 min-w-0">
                      <p
                        className="text-sm font-medium text-gray-900 truncate"
                        title={file.name}
                      >
                        {file.name}
                      </p>
                      <div className="flex items-center space-x-4 mt-1">
                        <p className="text-sm text-gray-500">{file.size}</p>
                        <p className="text-sm text-gray-500">
                          {file.pageCount} trang
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(file.uploadDate)}
                        </p>
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                            file.status
                          )}`}
                        >
                          <span className="mr-1">
                            {getStatusIcon(file.status)}
                          </span>
                          {file.status === "ready"
                            ? "Sẵn sàng"
                            : file.status === "processing"
                            ? "Đang xử lý"
                            : file.status === "completed"
                            ? "Hoàn thành"
                            : "Lỗi"}
                        </span>
                        {file.id === "1" && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            File mẫu
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePreview(file.name)}
                        className="p-2 text-gray-400 hover:text-gray-600"
                      >
                        <EyeIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Split Configuration */}
          {selectedFile && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">
                  Cấu hình tách file
                </h2>
              </div>
              <div className="p-6 space-y-6">
                {/* Split Mode Selection */}
                <div>
                  <label className="text-base font-medium text-gray-900">
                    Chế độ tách
                  </label>
                  <div className="mt-4 space-y-4">
                    <div className="flex items-center">
                      <input
                        id="by-pages"
                        name="split-mode"
                        type="radio"
                        checked={splitMode === "by-pages"}
                        onChange={() => setSplitMode("by-pages")}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <label
                        htmlFor="by-pages"
                        className="ml-3 block text-sm font-medium text-gray-700"
                      >
                        Tách theo số trang
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="custom"
                        name="split-mode"
                        type="radio"
                        checked={splitMode === "custom"}
                        onChange={() => setSplitMode("custom")}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                      />
                      <label
                        htmlFor="custom"
                        className="ml-3 block text-sm font-medium text-gray-700"
                      >
                        Tách theo tùy chọn
                      </label>
                    </div>
                  </div>
                </div>

                {/* By Pages Configuration */}
                {splitMode === "by-pages" && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Số trang mỗi file
                    </label>
                    <input
                      type="number"
                      min="1"
                      max={selectedFileData?.pageCount || 1}
                      value={pagesPerSplit}
                      onChange={(e) =>
                        setPagesPerSplit(parseInt(e.target.value) || 1)
                      }
                      className="block w-32 rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Sẽ tạo ra{" "}
                      {Math.ceil(
                        (selectedFileData?.pageCount || 1) / pagesPerSplit
                      )}{" "}
                      file
                    </p>
                  </div>
                )}

                {/* Custom Ranges Configuration */}
                {splitMode === "custom" && (
                  <div>
                    <div className="flex items-center justify-between mb-4">
                      <label className="block text-sm font-medium text-gray-700">
                        Phạm vi trang tùy chọn
                      </label>
                      <button
                        onClick={addCustomRange}
                        className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-green-700 bg-green-100 hover:bg-green-200"
                      >
                        <PlusIcon className="h-4 w-4 mr-1" />
                        Thêm phạm vi
                      </button>
                    </div>
                    <div className="space-y-3">
                      {customRanges.map((range) => (
                        <div
                          key={range.id}
                          className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg"
                        >
                          <input
                            type="text"
                            value={range.name}
                            onChange={(e) =>
                              updateCustomRange(
                                range.id,
                                "name",
                                e.target.value
                              )
                            }
                            className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                            placeholder="Tên"
                          />
                          <span className="text-sm text-gray-500">
                            từ trang
                          </span>
                          <input
                            type="number"
                            min="1"
                            max={selectedFileData?.pageCount || 1}
                            value={range.startPage}
                            onChange={(e) =>
                              updateCustomRange(
                                range.id,
                                "startPage",
                                parseInt(e.target.value) || 1
                              )
                            }
                            className="block w-20 rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                          />
                          <span className="text-sm text-gray-500">đến</span>
                          <input
                            type="number"
                            min="1"
                            max={selectedFileData?.pageCount || 1}
                            value={range.endPage}
                            onChange={(e) =>
                              updateCustomRange(
                                range.id,
                                "endPage",
                                parseInt(e.target.value) || 1
                              )
                            }
                            className="block w-20 rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500 sm:text-sm"
                          />
                          {customRanges.length > 1 && (
                            <button
                              onClick={() => removeCustomRange(range.id)}
                              className="p-1 text-red-400 hover:text-red-600"
                            >
                              <MinusIcon className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Processing Summary */}
        <div className="space-y-6">
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">
                Tóm tắt xử lý
              </h2>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">File đã chọn:</span>
                  <span
                    className="font-medium text-right max-w-48 truncate"
                    title={selectedFileData?.name}
                  >
                    {selectedFile ? selectedFileData?.name : "Chưa chọn"}
                  </span>
                </div>
                {selectedFile && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Tổng số trang:</span>
                      <span className="font-medium">
                        {selectedFileData?.pageCount}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Chế độ tách:</span>
                      <span className="font-medium">
                        {splitMode === "by-pages"
                          ? "Theo số trang"
                          : "Tùy chọn"}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Số file sẽ tạo:</span>
                      <span className="font-medium">
                        {splitMode === "by-pages"
                          ? Math.ceil(
                              (selectedFileData?.pageCount || 1) / pagesPerSplit
                            )
                          : customRanges.length}
                      </span>
                    </div>
                  </>
                )}
              </div>

              <button
                onClick={handleStartProcessing}
                disabled={!selectedFile || isProcessing}
                className="w-full mt-6 inline-flex justify-center items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isProcessing ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Đang xử lý...
                  </>
                ) : (
                  <>
                    <ScissorsIcon className="h-5 w-5 mr-2" />
                    Bắt đầu tách
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced PDF Preview Modal */}
      {previewFile && (
        <div
          className={`fixed inset-0 z-50 ${
            isFullscreen ? "bg-black" : "bg-gray-900 bg-opacity-50"
          }`}
        >
          <div
            className={`${
              isFullscreen
                ? "h-full"
                : "flex items-center justify-center min-h-screen p-4"
            }`}
          >
            {!isFullscreen && (
              <div
                className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                onClick={closePreview}
              ></div>
            )}

            <div
              className={`${
                isFullscreen
                  ? "w-full h-full bg-white"
                  : "relative bg-white rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
              }`}
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-green-600 to-blue-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <DocumentIcon className="h-6 w-6 text-white" />
                    <div>
                      <h3 className="text-lg font-semibold text-white truncate max-w-md">
                        {previewFile.name}
                      </h3>
                      <p className="text-green-100 text-sm">
                        PDF Document Preview
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Zoom Controls */}
                    <div className="flex items-center space-x-1 bg-white bg-opacity-20 rounded-lg px-3 py-1">
                      <button
                        onClick={zoomOut}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={zoomLevel <= 50}
                      >
                        <MagnifyingGlassMinusIcon className="h-4 w-4" />
                      </button>
                      <span className="text-white text-sm font-medium min-w-[3rem] text-center">
                        {zoomLevel}%
                      </span>
                      <button
                        onClick={zoomIn}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={zoomLevel >= 200}
                      >
                        <MagnifyingGlassPlusIcon className="h-4 w-4" />
                      </button>
                    </div>

                    {/* Page Navigation */}
                    <div className="flex items-center space-x-1 bg-white bg-opacity-20 rounded-lg px-3 py-1">
                      <button
                        onClick={prevPage}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={currentPage <= 1}
                      >
                        ←
                      </button>
                      <span className="text-white text-sm font-medium min-w-[3rem] text-center">
                        {currentPage}/{totalPages}
                      </span>
                      <button
                        onClick={nextPage}
                        className="p-1 text-white hover:bg-white hover:bg-opacity-20 rounded"
                        disabled={currentPage >= totalPages}
                      >
                        →
                      </button>
                    </div>

                    {/* Fullscreen Toggle */}
                    <button
                      onClick={toggleFullscreen}
                      className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                    >
                      {isFullscreen ? (
                        <ArrowsPointingInIcon className="h-5 w-5" />
                      ) : (
                        <ArrowsPointingOutIcon className="h-5 w-5" />
                      )}
                    </button>

                    {/* Close Button */}
                    <button
                      onClick={closePreview}
                      className="p-2 text-white hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
                    >
                      <XMarkIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>

              {/* PDF Viewer */}
              <div
                className={`${
                  isFullscreen ? "h-[calc(100vh-80px)]" : "h-[70vh]"
                } bg-gray-100`}
              >
                <iframe
                  src={`${previewFile.url}#toolbar=0&navpanes=0&scrollbar=1&page=${currentPage}&zoom=${zoomLevel}`}
                  className="w-full h-full border-0"
                  title={`Preview of ${previewFile.name}`}
                />
              </div>

              {/* Footer Actions */}
              {!isFullscreen && (
                <div className="bg-gray-50 px-6 py-4 border-t">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>Zoom: {zoomLevel}%</span>
                      <span>
                        Page: {currentPage} of {totalPages}
                      </span>
                    </div>

                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleDownload(previewFile.name)}
                        className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                        Download
                      </button>
                      <button
                        onClick={closePreview}
                        className="inline-flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 transition-colors"
                      >
                        Close
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
