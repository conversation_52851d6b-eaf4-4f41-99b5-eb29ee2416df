import { ApiClient } from '../core/apiClient';
import {
  SyncOptions,
  SyncStatusResponse,
  SyncJobDto,
  SyncTriggerResponse,
  SyncStatus,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export class SyncService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Trigger Google Drive sync for a file or all files
   * @param options Sync options
   * @returns Information about the created sync job
   */
  async triggerGoogleDriveSync(options?: SyncOptions): Promise<SyncTriggerResponse> {
    this.validateSyncOptions(options);

    try {
      return await this.client.post<SyncTriggerResponse>(
        '/sync/google-drive',
        options || {}
      );
    } catch (error) {
      throw this.handleSyncError(error as ApiError);
    }
  }

  /**
   * Trigger sync for any storage provider
   * @param provider Storage provider name (e.g., 'GoogleDrive', 'CloudflareR2')
   * @param options Sync options
   * @returns Information about the created sync job
   */
  async triggerSync(provider: string, options?: SyncOptions): Promise<SyncTriggerResponse> {
    this.validateProvider(provider);
    this.validateSyncOptions(options);

    try {
      return await this.client.post<SyncTriggerResponse>(
        `/sync/${provider.toLowerCase()}`,
        options || {}
      );
    } catch (error) {
      throw this.handleSyncError(error as ApiError);
    }
  }

  /**
   * Get sync status for files
   * @param fileId Optional file ID to filter status by specific file
   * @param provider Storage provider (default: 'GoogleDrive')
   * @returns Sync status information
   */
  async getSyncStatus(fileId?: string, provider: string = 'GoogleDrive'): Promise<SyncStatusResponse> {
    this.validateProvider(provider);
    if (fileId) this.validateId(fileId, 'fileId');

    const params: Record<string, any> = { provider };
    if (fileId) params.fileId = fileId;

    try {
      return await this.client.get<SyncStatusResponse>('/sync/status', { params });
    } catch (error) {
      throw this.handleSyncStatusError(error as ApiError);
    }
  }

  /**
   * Get overall sync health and statistics
   * @returns Sync health information
   */
  async getSyncHealth(): Promise<{
    googleDrive: {
      isConnected: boolean;
      lastSync: string;
      errorCount: number;
    };
    cloudflareR2: {
      isConnected: boolean;
      lastSync: string;
      errorCount: number;
    };
    totalPendingJobs: number;
    totalFailedJobs: number;
  }> {
    try {
      return await this.client.get('/sync/health');
    } catch (error) {
      throw this.handleSyncHealthError(error as ApiError);
    }
  }

  /**
   * Get detailed information about a specific sync job
   * @param jobId ID of the sync job
   * @returns Detailed job information
   */
  async getSyncJobDetails(jobId: string): Promise<SyncJobDto> {
    this.validateId(jobId, 'jobId');

    try {
      return await this.client.get<SyncJobDto>(`/sync/jobs/${jobId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Sync job');
    }
  }

  /**
   * List all sync jobs with filtering and pagination
   * @param options Query options
   * @returns List of sync jobs
   */
  async listSyncJobs(options?: {
    status?: SyncStatus;
    provider?: string;
    fileId?: string;
    page?: number;
    pageSize?: number;
    fromDate?: string;
    toDate?: string;
  }): Promise<{
    jobs: SyncJobDto[];
    pagination: {
      page: number;
      pageSize: number;
      totalItems: number;
      totalPages: number;
    };
  }> {
    this.validateListOptions(options);

    const params: Record<string, any> = {};
    if (options?.status) params.status = options.status;
    if (options?.provider) params.provider = options.provider;
    if (options?.fileId) params.fileId = options.fileId;
    if (options?.page) params.page = options.page;
    if (options?.pageSize) params.pageSize = options.pageSize;
    if (options?.fromDate) params.fromDate = options.fromDate;
    if (options?.toDate) params.toDate = options.toDate;

    try {
      return await this.client.get('/sync/jobs', { params });
    } catch (error) {
      throw this.handleListError(error as ApiError);
    }
  }

  /**
   * Cancel a sync job
   * @param jobId ID of the sync job to cancel
   * @returns Status of the cancellation
   */
  async cancelSync(jobId: string): Promise<{ success: boolean; message: string }> {
    this.validateId(jobId, 'jobId');

    try {
      return await this.client.post<{ success: boolean; message: string }>(
        `/sync/jobs/${jobId}/cancel`
      );
    } catch (error) {
      throw this.handleCancelError(error as ApiError);
    }
  }

  /**
   * Retry a failed sync job
   * @param jobId ID of the sync job to retry
   * @returns Information about the new retry job
   */
  async retrySync(jobId: string): Promise<SyncTriggerResponse> {
    this.validateId(jobId, 'jobId');

    try {
      return await this.client.post<SyncTriggerResponse>(
        `/sync/jobs/${jobId}/retry`
      );
    } catch (error) {
      throw this.handleRetryError(error as ApiError);
    }
  }

  /**
   * Get sync history for a file
   * @param fileId ID of the file
   * @param limit Maximum number of history entries (default: 10)
   * @param provider Optional provider filter
   * @returns List of past sync jobs for the file
   */
  async getSyncHistory(
    fileId: string,
    limit: number = 10,
    provider?: string
  ): Promise<SyncJobDto[]> {
    this.validateId(fileId, 'fileId');
    this.validateLimit(limit);
    if (provider) this.validateProvider(provider);

    const params: Record<string, any> = { limit };
    if (provider) params.provider = provider;

    try {
      return await this.client.get<SyncJobDto[]>(`/sync/history/${fileId}`, {
        params
      });
    } catch (error) {
      throw this.handleHistoryError(error as ApiError);
    }
  }

  /**
   * Pause all sync operations
   * @returns Status of the pause operation
   */
  async pauseAllSync(): Promise<{ success: boolean; message: string }> {
    try {
      return await this.client.post<{ success: boolean; message: string }>('/sync/pause');
    } catch (error) {
      throw this.handleControlError(error as ApiError, 'pause');
    }
  }

  /**
   * Resume all sync operations
   * @returns Status of the resume operation
   */
  async resumeAllSync(): Promise<{ success: boolean; message: string }> {
    try {
      return await this.client.post<{ success: boolean; message: string }>('/sync/resume');
    } catch (error) {
      throw this.handleControlError(error as ApiError, 'resume');
    }
  }

  /**
   * Configure sync settings
   * @param settings Sync configuration
   * @returns Updated configuration
   */
  async configureSyncSettings(settings: {
    autoSyncEnabled?: boolean;
    syncInterval?: number; // minutes
    maxConcurrentJobs?: number;
    retryAttempts?: number;
    providers?: {
      googleDrive?: {
        enabled: boolean;
        autoSync: boolean;
      };
      cloudflareR2?: {
        enabled: boolean;
        autoSync: boolean;
      };
    };
  }): Promise<any> {
    this.validateSyncSettings(settings);

    try {
      return await this.client.put('/sync/settings', settings);
    } catch (error) {
      throw this.handleConfigError(error as ApiError);
    }
  }

  /**
   * Get current sync settings
   * @returns Current sync configuration
   */
  async getSyncSettings(): Promise<{
    autoSyncEnabled: boolean;
    syncInterval: number;
    maxConcurrentJobs: number;
    retryAttempts: number;
    providers: {
      googleDrive: {
        enabled: boolean;
        autoSync: boolean;
        isConnected: boolean;
      };
      cloudflareR2: {
        enabled: boolean;
        autoSync: boolean;
        isConnected: boolean;
      };
    };
  }> {
    try {
      return await this.client.get('/sync/settings');
    } catch (error) {
      throw this.handleConfigError(error as ApiError);
    }
  }

  // Validation methods

  private validateId(id: string, fieldName: string): void {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationApiError(`Invalid ${fieldName}`, [
        { field: fieldName, message: `${fieldName} is required and must be a non-empty string`, code: 'REQUIRED' }
      ]);
    }
  }

  private validateProvider(provider: string): void {
    if (!provider || typeof provider !== 'string') {
      throw new ValidationApiError('Invalid provider', [
        { field: 'provider', message: 'Provider is required and must be a string', code: 'REQUIRED' }
      ]);
    }

    const validProviders = ['GoogleDrive', 'CloudflareR2', 'googledrive', 'cloudflarer2'];
    if (!validProviders.includes(provider)) {
      throw new ValidationApiError('Invalid provider', [
        { field: 'provider', message: 'Provider must be GoogleDrive or CloudflareR2', code: 'INVALID_VALUE' }
      ]);
    }
  }

  private validateSyncOptions(options?: SyncOptions): void {
    if (!options) return;

    if (options.fileId && typeof options.fileId !== 'string') {
      throw new ValidationApiError('Invalid fileId', [
        { field: 'fileId', message: 'fileId must be a string', code: 'INVALID_TYPE' }
      ]);
    }

    if (options.forceSync !== undefined && typeof options.forceSync !== 'boolean') {
      throw new ValidationApiError('Invalid forceSync', [
        { field: 'forceSync', message: 'forceSync must be a boolean', code: 'INVALID_TYPE' }
      ]);
    }
  }

  private validateLimit(limit: number): void {
    if (!Number.isInteger(limit) || limit < 1 || limit > 100) {
      throw new ValidationApiError('Invalid limit', [
        { field: 'limit', message: 'Limit must be an integer between 1 and 100', code: 'INVALID_VALUE' }
      ]);
    }
  }

  private validateListOptions(options?: any): void {
    if (!options) return;

    if (options.page && (!Number.isInteger(options.page) || options.page < 1)) {
      throw new ValidationApiError('Invalid page', [
        { field: 'page', message: 'Page must be a positive integer', code: 'INVALID_VALUE' }
      ]);
    }

    if (options.pageSize && (!Number.isInteger(options.pageSize) || options.pageSize < 1 || options.pageSize > 100)) {
      throw new ValidationApiError('Invalid pageSize', [
        { field: 'pageSize', message: 'Page size must be between 1 and 100', code: 'INVALID_VALUE' }
      ]);
    }

    if (options.fromDate && !this.isValidDate(options.fromDate)) {
      throw new ValidationApiError('Invalid fromDate', [
        { field: 'fromDate', message: 'fromDate must be a valid ISO date string', code: 'INVALID_FORMAT' }
      ]);
    }

    if (options.toDate && !this.isValidDate(options.toDate)) {
      throw new ValidationApiError('Invalid toDate', [
        { field: 'toDate', message: 'toDate must be a valid ISO date string', code: 'INVALID_FORMAT' }
      ]);
    }
  }

  private validateSyncSettings(settings: any): void {
    if (!settings || Object.keys(settings).length === 0) {
      throw new ValidationApiError('Settings are required', [
        { field: 'settings', message: 'At least one setting must be provided', code: 'REQUIRED' }
      ]);
    }

    if (settings.syncInterval !== undefined && (!Number.isInteger(settings.syncInterval) || settings.syncInterval < 1)) {
      throw new ValidationApiError('Invalid sync interval', [
        { field: 'syncInterval', message: 'Sync interval must be a positive integer (minutes)', code: 'INVALID_VALUE' }
      ]);
    }

    if (settings.maxConcurrentJobs !== undefined && (!Number.isInteger(settings.maxConcurrentJobs) || settings.maxConcurrentJobs < 1 || settings.maxConcurrentJobs > 10)) {
      throw new ValidationApiError('Invalid max concurrent jobs', [
        { field: 'maxConcurrentJobs', message: 'Max concurrent jobs must be between 1 and 10', code: 'INVALID_VALUE' }
      ]);
    }
  }

  private isValidDate(dateString: string): boolean {
    try {
      const date = new Date(dateString);
      return date.toISOString() === dateString;
    } catch {
      return false;
    }
  }

  // Error handling methods

  private handleSyncError(error: ApiError): ApiError {
    if (error.statusCode === 503) {
      return new ApiError('Sync service unavailable', 503, ErrorCode.SYNC_PROVIDER_UNAVAILABLE, error.correlationId);
    }
    if (error.statusCode === 401) {
      return new ApiError('Sync authentication failed', 401, ErrorCode.SYNC_AUTHENTICATION_FAILED, error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Sync quota exceeded', 507, ErrorCode.SYNC_QUOTA_EXCEEDED, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions for sync', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleSyncStatusError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view sync status', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleSyncHealthError(error: ApiError): ApiError {
    if (error.statusCode === 503) {
      return new ApiError('Sync health service unavailable', 503, ErrorCode.SYNC_PROVIDER_UNAVAILABLE, error.correlationId);
    }
    return error;
  }

  private handleCancelError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Cannot cancel sync job in current state', 409, 'INVALID_SYNC_STATE', error.correlationId);
    }
    return this.handleNotFoundError(error, 'Sync job');
  }

  private handleRetryError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Cannot retry sync job in current state', 409, 'INVALID_SYNC_STATE', error.correlationId);
    }
    return this.handleNotFoundError(error, 'Sync job');
  }

  private handleHistoryError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to view sync history', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'File');
  }

  private handleControlError(error: ApiError, operation: string): ApiError {
    if (error.statusCode === 403) {
      return new ApiError(`Insufficient permissions to ${operation} sync`, 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 409) {
      return new ApiError(`Cannot ${operation} sync in current state`, 409, 'INVALID_SYNC_STATE', error.correlationId);
    }
    return error;
  }

  private handleConfigError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to modify sync settings', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleListError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to list sync jobs', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleNotFoundError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }
}

