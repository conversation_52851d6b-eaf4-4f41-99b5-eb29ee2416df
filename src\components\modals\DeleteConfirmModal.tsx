"use client";

import React, { useState } from "react";
import { Modal } from "@/components/ui/Modal";
import {
  ExclamationTriangleIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  itemName?: string;
  itemType?: string;
  isLoading?: boolean;
}

export function DeleteConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title = "Xác nhận xóa",
  message,
  itemName,
  itemType = "mục",
  isLoading = false,
}: DeleteConfirmModalProps) {
  const [confirmText, setConfirmText] = useState("");
  const [requireConfirmText] = useState(itemName && itemName.length > 0);

  const handleConfirm = () => {
    if (requireConfirmText && confirmText !== itemName) {
      return;
    }
    onConfirm();
  };

  const handleClose = () => {
    setConfirmText("");
    onClose();
  };

  const isConfirmDisabled = requireConfirmText
    ? confirmText !== itemName
    : false;

  const defaultMessage = itemName
    ? `Bạn có chắc chắn muốn xóa ${itemType} "${itemName}"? Hành động này không thể hoàn tác.`
    : `Bạn có chắc chắn muốn xóa ${itemType} này? Hành động này không thể hoàn tác.`;

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={title}
      size="md"
      showCloseButton={false}
    >
      <div className="space-y-6">
        {/* Warning Icon */}
        <div className="flex justify-center">
          <div className="p-3 bg-red-100 rounded-full">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-600" />
          </div>
        </div>

        {/* Message */}
        <div className="text-center">
          <p className="text-gray-700 leading-relaxed">
            {message || defaultMessage}
          </p>
        </div>

        {/* Item Details */}
        {itemName && (
          <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <TrashIcon className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {itemType.charAt(0).toUpperCase() + itemType.slice(1)} sẽ bị
                  xóa:
                </p>
                <p className="text-sm text-gray-600 font-mono bg-white px-2 py-1 rounded border mt-1">
                  {itemName}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Confirmation Input */}
        {requireConfirmText && (
          <div className="space-y-3">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-yellow-800">
                    Xác nhận bằng cách nhập tên
                  </p>
                  <p className="text-sm text-yellow-700 mt-1">
                    Để xác nhận, vui lòng nhập chính xác:{" "}
                    <span className="font-mono font-semibold">{itemName}</span>
                  </p>
                </div>
              </div>
            </div>

            <div>
              <label
                htmlFor="confirmText"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Nhập tên để xác nhận:
              </label>
              <input
                type="text"
                id="confirmText"
                value={confirmText}
                onChange={(e) => setConfirmText(e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors ${
                  confirmText && confirmText !== itemName
                    ? "border-red-300 bg-red-50"
                    : "border-gray-300"
                }`}
                placeholder={`Nhập "${itemName}" để xác nhận`}
                autoComplete="off"
              />
              {confirmText && confirmText !== itemName && (
                <p className="mt-1 text-sm text-red-600">
                  Tên không khớp. Vui lòng nhập chính xác.
                </p>
              )}
            </div>
          </div>
        )}

        {/* Warning Notice */}
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-red-800">
                Cảnh báo: Hành động không thể hoàn tác
              </p>
              <ul className="text-sm text-red-700 mt-2 space-y-1">
                <li>• Dữ liệu sẽ bị xóa vĩnh viễn</li>
                <li>• Không thể khôi phục sau khi xóa</li>
                <li>• Các dữ liệu liên quan có thể bị ảnh hưởng</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={handleClose}
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <XMarkIcon className="h-4 w-4 mr-2 inline" />
            Hủy
          </button>
          <button
            type="button"
            onClick={handleConfirm}
            disabled={isConfirmDisabled || isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Đang xóa...</span>
              </>
            ) : (
              <>
                <TrashIcon className="h-4 w-4" />
                <span>Xóa vĩnh viễn</span>
              </>
            )}
          </button>
        </div>
      </div>
    </Modal>
  );
}
