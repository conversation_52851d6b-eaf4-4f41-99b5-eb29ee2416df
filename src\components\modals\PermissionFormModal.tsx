"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import {
  ShieldCheckIcon,
  PlusIcon,
  PencilIcon,
  EyeIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  UserGroupIcon,
  CogIcon,
} from "@heroicons/react/24/outline";

interface Permission {
  id: string;
  name: string;
  description: string;
  category: string;
  actions: string[];
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
}

interface PermissionFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: "add" | "edit" | "view";
  permission?: Permission;
  onSubmit?: (permission: Permission) => void;
}

const PERMISSION_CATEGORIES = [
  {
    value: "user",
    label: "Quản lý người dùng",
    icon: UserGroupIcon,
    color: "blue",
  },
  {
    value: "file",
    label: "Quản lý file",
    icon: ShieldCheckIcon,
    color: "green",
  },
  {
    value: "system",
    label: "<PERSON>u<PERSON><PERSON> lý hệ thống",
    icon: CogIcon,
    color: "purple",
  },
  { value: "report", label: "Báo cáo", icon: CheckCircleIcon, color: "orange" },
];

const AVAILABLE_ACTIONS = [
  { value: "view", label: "Xem", description: "Xem danh sách và chi tiết" },
  { value: "create", label: "Tạo mới", description: "Tạo mới dữ liệu" },
  { value: "edit", label: "Chỉnh sửa", description: "Cập nhật thông tin" },
  { value: "delete", label: "Xóa", description: "Xóa dữ liệu" },
  { value: "export", label: "Xuất dữ liệu", description: "Xuất file, báo cáo" },
  {
    value: "import",
    label: "Nhập dữ liệu",
    description: "Import dữ liệu từ file",
  },
  { value: "approve", label: "Phê duyệt", description: "Phê duyệt yêu cầu" },
  {
    value: "assign",
    label: "Phân quyền",
    description: "Gán quyền cho người khác",
  },
];

export function PermissionFormModal({
  isOpen,
  onClose,
  mode,
  permission,
  onSubmit,
}: PermissionFormModalProps) {
  const [formData, setFormData] = useState<
    Omit<Permission, "id"> & { id?: string }
  >({
    name: "",
    description: "",
    category: "",
    actions: [],
    isActive: true,
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when modal opens/closes or permission changes
  useEffect(() => {
    if (isOpen && permission && (mode === "edit" || mode === "view")) {
      setFormData(permission);
    } else if (isOpen && mode === "add") {
      setFormData({
        name: "",
        description: "",
        category: "",
        actions: [],
        isActive: true,
      });
    }
    setErrors({});
  }, [isOpen, permission, mode]);

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]:
        type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({ ...prev, [name]: "" }));
    }
  };

  const handleActionToggle = (actionValue: string) => {
    if (mode === "view") return;

    setFormData((prev) => ({
      ...prev,
      actions: prev.actions.includes(actionValue)
        ? prev.actions.filter((a) => a !== actionValue)
        : [...prev.actions, actionValue],
    }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = "Tên quyền là bắt buộc";
    if (!formData.description.trim())
      newErrors.description = "Mô tả là bắt buộc";
    if (!formData.category) newErrors.category = "Danh mục là bắt buộc";
    if (formData.actions.length === 0)
      newErrors.actions = "Phải chọn ít nhất một hành động";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (mode === "view") return;
    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (onSubmit) {
        onSubmit({
          ...formData,
          id: permission?.id || `perm_${Date.now()}`,
          createdAt: permission?.createdAt || new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        });
      }

      onClose();
    } catch (error) {
      console.error("Error saving permission:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getModalTitle = () => {
    switch (mode) {
      case "add":
        return "Thêm quyền mới";
      case "edit":
        return "Chỉnh sửa quyền";
      case "view":
        return "Chi tiết quyền";
      default:
        return "Quyền";
    }
  };

  const getModalIcon = () => {
    switch (mode) {
      case "add":
        return PlusIcon;
      case "edit":
        return PencilIcon;
      case "view":
        return EyeIcon;
      default:
        return ShieldCheckIcon;
    }
  };

  const ModalIcon = getModalIcon();
  const isReadOnly = mode === "view";
  const selectedCategory = PERMISSION_CATEGORIES.find(
    (cat) => cat.value === formData.category
  );

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={getModalTitle()} size="xl">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Permission Icon */}
        <div className="flex justify-center">
          <div
            className={`relative p-4 rounded-full ${
              selectedCategory
                ? `bg-${selectedCategory.color}-100`
                : "bg-gray-100"
            }`}
          >
            {selectedCategory ? (
              <selectedCategory.icon
                className={`h-8 w-8 text-${selectedCategory.color}-600`}
              />
            ) : (
              <ShieldCheckIcon className="h-8 w-8 text-gray-600" />
            )}
          </div>
        </div>

        {/* Basic Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <ShieldCheckIcon className="h-5 w-5 text-blue-600" />
            </div>
            Thông tin cơ bản
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Tên quyền *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                readOnly={isReadOnly}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.name ? "border-red-300 bg-red-50" : "border-gray-300"
                } ${isReadOnly ? "bg-gray-50 cursor-not-allowed" : ""}`}
                placeholder="Nhập tên quyền"
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <label
                htmlFor="category"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Danh mục *
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                disabled={isReadOnly}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                  errors.category
                    ? "border-red-300 bg-red-50"
                    : "border-gray-300"
                } ${isReadOnly ? "bg-gray-50 cursor-not-allowed" : ""}`}
              >
                <option value="">Chọn danh mục</option>
                {PERMISSION_CATEGORIES.map((category) => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
              {errors.category && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.category}
                </p>
              )}
            </div>
          </div>

          <div>
            <label
              htmlFor="description"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Mô tả *
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              value={formData.description}
              onChange={handleInputChange}
              readOnly={isReadOnly}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                errors.description
                  ? "border-red-300 bg-red-50"
                  : "border-gray-300"
              } ${isReadOnly ? "bg-gray-50 cursor-not-allowed" : ""}`}
              placeholder="Mô tả chi tiết về quyền này"
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600 flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {errors.description}
              </p>
            )}
          </div>
        </div>

        {/* Actions Selection */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900 flex items-center">
            <div className="p-2 bg-green-100 rounded-lg mr-3">
              <CheckCircleIcon className="h-5 w-5 text-green-600" />
            </div>
            Hành động được phép
          </h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {AVAILABLE_ACTIONS.map((action) => (
              <div
                key={action.value}
                className={`border rounded-lg p-3 cursor-pointer transition-all ${
                  formData.actions.includes(action.value)
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-200 hover:border-gray-300"
                } ${isReadOnly ? "cursor-not-allowed opacity-75" : ""}`}
                onClick={() => handleActionToggle(action.value)}
              >
                <div className="flex items-start space-x-3">
                  <div
                    className={`w-5 h-5 rounded border-2 flex items-center justify-center mt-0.5 ${
                      formData.actions.includes(action.value)
                        ? "border-blue-500 bg-blue-500"
                        : "border-gray-300"
                    }`}
                  >
                    {formData.actions.includes(action.value) && (
                      <CheckCircleIcon className="h-3 w-3 text-white" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h5 className="text-sm font-medium text-gray-900">
                      {action.label}
                    </h5>
                    <p className="text-xs text-gray-500 mt-1">
                      {action.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {errors.actions && (
            <p className="mt-1 text-sm text-red-600 flex items-center">
              <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
              {errors.actions}
            </p>
          )}
        </div>

        {/* Status */}
        {!isReadOnly && (
          <div className="flex items-center">
            <input
              id="isActive"
              name="isActive"
              type="checkbox"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="isActive" className="ml-2 text-sm text-gray-700">
              Kích hoạt quyền này
            </label>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {isReadOnly ? "Đóng" : "Hủy"}
          </button>
          {!isReadOnly && (
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>
                    {mode === "add" ? "Đang tạo..." : "Đang cập nhật..."}
                  </span>
                </>
              ) : (
                <>
                  <ModalIcon className="h-4 w-4" />
                  <span>{mode === "add" ? "Tạo quyền" : "Cập nhật"}</span>
                </>
              )}
            </button>
          )}
        </div>
      </form>
    </Modal>
  );
}
