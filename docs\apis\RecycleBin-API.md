# Recycle Bin API - Thùng Rác

## Tổng quan
API quản lý thùng rác cho phép người dùng xem, khôi phục và xóa vĩnh viễn các file/folder đã xóa trong vòng 30 ngày.

**Base URL:** `/api/v1/recycle-bin`

---

## 1. <PERSON><PERSON><PERSON> danh sách thùng rác

### `GET /api/v1/recycle-bin`

L<PERSON>y danh sách phân trang các file/folder đã xóa có thể khôi phục trong 30 ngày.

#### Request Parameters

| Tham số | Kiểu | Bắt buộc | Mặc định | Mô tả |
|---------|------|----------|----------|-------|
| `page` | integer | Không | 1 | Số trang (bắt đầu từ 1) |
| `pageSize` | integer | Không | 20 | Số item mỗi trang (1-100) |
| `itemType` | integer | Không | - | Lọc theo loại: 1=Folder, 2=File |
| `searchTerm` | string | Không | - | Tìm kiếm theo tên hoặc đường dẫn |
| `uploaderEmail` | string | Không | - | Lọc theo email người tạo |
| `deletedAfter` | date | Không | - | Lọc từ ngày xóa (YYYY-MM-DD) |
| `deletedBefore` | date | Không | - | Lọc đến ngày xóa (YYYY-MM-DD) |
| `onlyRestorable` | boolean | Không | true | Chỉ hiển thị item có thể khôi phục |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "originalId": "456e7890-e89b-12d3-a456-************",
        "itemType": 2,
        "itemTypeDisplay": "Tệp tin",
        "itemIcon": "pdf",
        "originalName": "Nghị quyết chính phủ.pdf",
        "originalPath": "/Phòng ban A/Hồ sơ cá/Nghị quyết chính phủ.pdf",
        "displayPath": "Phòng ban A > Hồ sơ cá > Nghị quyết chính phủ.pdf",
        "parentFolderId": "789e1234-e89b-12d3-a456-************",
        "deletedBy": "abc1234-e89b-12d3-a456-************",
        "deletedByEmail": "<EMAIL>",
        "deletedAt": "2023-03-21T22:09:00Z",
        "deletedAtDisplay": "21/03/2023 22:09",
        "restoredAt": null,
        "restoredBy": null,
        "canRestore": true,
        "daysRemaining": 27,
        "statusDisplay": "Còn 27 ngày",
        "statusClass": "success",
        "originalSize": 3072,
        "originalContentType": "application/pdf",
        "formattedSize": "3 KB",
        "sizeDisplay": "3 KB"
      }
    ],
    "totalCount": 15,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Lấy danh sách thành công (có thể rỗng)

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `400 Bad Request`: Tham số không hợp lệ
- `500 Internal Server Error`: Lỗi server

#### Ví dụ sử dụng

```bash
# Lấy trang đầu tiên
GET /api/v1/recycle-bin

# Tìm kiếm file PDF
GET /api/v1/recycle-bin?searchTerm=.pdf&itemType=2

# Lọc theo khoảng thời gian
GET /api/v1/recycle-bin?deletedAfter=2023-03-01&deletedBefore=2023-03-31

# Lấy tất cả (bao gồm hết hạn)
GET /api/v1/recycle-bin?onlyRestorable=false
```

---

## 2. Khôi phục item

### `POST /api/v1/recycle-bin/{deletedItemId}/restore`

Khôi phục file hoặc folder từ thùng rác về vị trí ban đầu hoặc vị trí mới.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `deletedItemId` | UUID | Có | ID của deleted item |

#### Request Body (Optional)

```json
{
  "newParentFolderId": "789e1234-e89b-12d3-a456-************"
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `newParentFolderId` | UUID | Không | ID folder đích (nếu khôi phục về vị trí khác) |

#### Response Body

**Thành công:**
```json
{
  "success": true,
  "message": "File 'Nghị quyết chính phủ.pdf' restored successfully",
  "data": true
}
```

**Lỗi:**
```json
{
  "success": false,
  "message": "Deleted item not found",
  "errorCode": "NOT_FOUND",
  "data": false
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Khôi phục thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền khôi phục item này
- `404 Not Found`: Deleted item không tồn tại
- `400 Bad Request`: Item hết hạn (>30 ngày), folder đích không tồn tại
- `409 Conflict`: Đã có file/folder cùng tên tại vị trí khôi phục
- `500 Internal Server Error`: Lỗi server

#### Ví dụ sử dụng

```bash
# Khôi phục về vị trí ban đầu
POST /api/v1/recycle-bin/123e4567-e89b-12d3-a456-************/restore

# Khôi phục về folder khác
POST /api/v1/recycle-bin/123e4567-e89b-12d3-a456-************/restore
Content-Type: application/json

{
  "newParentFolderId": "789e1234-e89b-12d3-a456-************"
}
```

---

## 3. Xóa vĩnh viễn item

### `DELETE /api/v1/recycle-bin/{deletedItemId}/permanent`

Xóa vĩnh viễn file hoặc folder khỏi thùng rác và database. **Thao tác này không thể hoàn tác.**

> **⚠️ Lưu ý:** Files trên R2 storage sẽ được bảo toàn để đảm bảo an toàn dữ liệu.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `deletedItemId` | UUID | Có | ID của deleted item |

#### Response Body

**Thành công:**
```json
{
  "success": true,
  "message": "File 'Nghị quyết chính phủ.pdf' has been permanently deleted",
  "data": true
}
```

**Lỗi:**
```json
{
  "success": false,
  "message": "Deleted item not found",
  "errorCode": "NOT_FOUND",
  "data": false
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Xóa vĩnh viễn thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền xóa item này
- `404 Not Found`: Deleted item không tồn tại
- `400 Bad Request`: Không thể xóa item này
- `500 Internal Server Error`: Lỗi server

#### Ví dụ sử dụng

```bash
DELETE /api/v1/recycle-bin/123e4567-e89b-12d3-a456-************/permanent
```

---

## 4. Thống kê thùng rác

### `GET /api/v1/recycle-bin/statistics`

Lấy thống kê tổng quan về thùng rác của người dùng.

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "totalItems": 25,
    "totalFiles": 18,
    "totalFolders": 7,
    "totalSize": 157286400,
    "formattedTotalSize": "150 MB",
    "restorableItems": 20,
    "expiredItems": 5,
    "itemsExpiringSoon": 3,
    "oldestItemDate": "2023-02-15T10:30:00Z",
    "newestItemDate": "2023-03-22T15:45:00Z"
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Lấy thống kê thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `500 Internal Server Error`: Lỗi server

---

## 5. Dọn dẹp items hết hạn

### `DELETE /api/v1/recycle-bin/empty`

Xóa vĩnh viễn tất cả items đã hết hạn (>30 ngày) khỏi thùng rác.

#### Response Body

```json
{
  "success": true,
  "message": "Cleaned up 5 expired items from recycle bin",
  "data": {
    "deletedCount": 5,
    "deletedItems": [
      {
        "id": "expired-item-1",
        "name": "old-file.txt",
        "deletedAt": "2023-01-15T10:00:00Z"
      }
    ]
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Dọn dẹp thành công (có thể không có item nào bị xóa)

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `500 Internal Server Error`: Lỗi server

---

## Workflow sử dụng

### 1. Quy trình xóa và khôi phục
```
1. User xóa file/folder → Tự động vào recycle bin
2. GET /recycle-bin → Xem danh sách đã xóa
3. POST /recycle-bin/{id}/restore → Khôi phục nếu cần
4. DELETE /recycle-bin/{id}/permanent → Xóa vĩnh viễn nếu chắc chắn
```

### 2. Quy trình quản lý thùng rác
```
1. GET /recycle-bin/statistics → Xem tổng quan
2. GET /recycle-bin?onlyRestorable=false → Xem tất cả items
3. DELETE /recycle-bin/empty → Dọn dẹp hết hạn
```

### 3. Tìm kiếm và lọc
```
1. Tìm theo tên: ?searchTerm=document
2. Lọc theo loại: ?itemType=2 (files only)
3. Lọc theo thời gian: ?deletedAfter=2023-03-01&deletedBefore=2023-03-31
4. Kết hợp filters: ?searchTerm=pdf&itemType=2&deletedAfter=2023-03-01
```

## Error Codes tổng hợp

| Code | HTTP Status | Mô tả |
|------|-------------|-------|
| `NOT_FOUND` | 404 | Deleted item không tồn tại |
| `FORBIDDEN` | 403 | Không có quyền truy cập |
| `EXPIRED` | 400 | Item đã hết hạn (>30 ngày) |
| `CONFLICT` | 409 | Xung đột tên file khi khôi phục |
| `INVALID_FOLDER` | 400 | Folder đích không hợp lệ |
| `ALREADY_RESTORED` | 400 | Item đã được khôi phục |
| `RESTORE_FAILED` | 500 | Lỗi trong quá trình khôi phục |
| `DELETE_FAILED` | 500 | Lỗi trong quá trình xóa vĩnh viễn |

## Rate Limiting

- **Restore operations:** 10 requests/minute
- **Permanent delete:** 5 requests/minute
- **List operations:** 60 requests/minute
- **Statistics:** 20 requests/minute
