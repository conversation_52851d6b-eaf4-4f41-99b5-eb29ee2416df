# Folder Management API - <PERSON><PERSON><PERSON>n lý Thư mục

## Tổng quan
API quản lý thư mục cung cấp đầy đủ tính năng tạo, xem, di chuyển, x<PERSON><PERSON> thư mục và quản lý quyền truy cập.

**Base URL:** `/api/v1/folders`

---

## 1. <PERSON><PERSON><PERSON> thư mục mới

### `POST /api/v1/folders`

T<PERSON><PERSON> thư mục mới trong hệ thống.

#### Request Body

```json
{
  "name": "Báo cáo tháng 3",
  "description": "Th<PERSON> mục chứa các báo cáo tháng 3/2023",
  "parentFolderId": "456e7890-e89b-12d3-a456-************",
  "isPublic": false,
  "tags": ["báo-cáo", "tháng-3", "2023"]
}
```

| Trường | <PERSON><PERSON><PERSON> | <PERSON><PERSON><PERSON> bu<PERSON> | <PERSON><PERSON> tả |
|--------|------|----------|-------|
| `name` | string | C<PERSON> | Tên thư mục (1-255 ký tự) |
| `description` | string | Không | Mô tả thư mục |
| `parentFolderId` | UUID | Không | ID thư mục cha (null = root) |
| `isPublic` | boolean | Không | Thư mục công khai |
| `tags` | string[] | Không | Danh sách tags |

#### Response Body

**Thành công:**
```json
{
  "success": true,
  "message": "Folder created successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Báo cáo tháng 3",
    "description": "Thư mục chứa các báo cáo tháng 3/2023",
    "path": "/Phòng ban A/Báo cáo tháng 3",
    "parentFolderId": "456e7890-e89b-12d3-a456-************",
    "ownerId": "user123-456-789",
    "isPublic": false,
    "tags": ["báo-cáo", "tháng-3", "2023"],
    "createdAt": "2023-03-22T10:30:00Z",
    "updatedAt": "2023-03-22T10:30:00Z",
    "itemCount": 0,
    "totalSize": 0,
    "version": 1
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `201 Created`: Tạo thư mục thành công

**❌ Lỗi:**
- `400 Bad Request`: Tên thư mục không hợp lệ, tên trùng
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền tạo thư mục trong folder cha
- `404 Not Found`: Thư mục cha không tồn tại
- `409 Conflict`: Thư mục cùng tên đã tồn tại

#### Ví dụ sử dụng

```bash
# Tạo thư mục trong root
POST /api/v1/folders
{
  "name": "Documents",
  "description": "Thư mục tài liệu chính"
}

# Tạo thư mục con
POST /api/v1/folders
{
  "name": "Báo cáo Q1",
  "parentFolderId": "456e7890-e89b-12d3-a456-************",
  "isPublic": true
}
```

---

## 2. Lấy danh sách thư mục

### `GET /api/v1/folders/my-folders`

Lấy danh sách thư mục của người dùng với filtering và phân trang nâng cao.

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mặc định | Mô tả |
|---------|------|----------|----------|-------|
| `page` | integer | Không | 1 | Số trang (bắt đầu từ 1) |
| `pageSize` | integer | Không | 20 | Số items/trang (1-100) |
| `parentFolderId` | UUID | Không | - | ID thư mục cha (null = root) |
| `searchTerm` | string | Không | - | Tìm kiếm theo tên thư mục |
| `uploaderEmail` | string | Không | - | Lọc theo email người tạo |
| `folderType` | string | Không | - | Loại thư mục: `public`, `private`, `shared` |
| `createdAfter` | date | Không | - | Tạo sau ngày (YYYY-MM-DD) |
| `createdBefore` | date | Không | - | Tạo trước ngày (YYYY-MM-DD) |
| `includeShared` | boolean | Không | false | Bao gồm thư mục được chia sẻ |
| `sortBy` | string | Không | `createdAt` | Sắp xếp: `name`, `createdAt`, `itemCount`, `totalSize` |
| `sortOrder` | string | Không | `desc` | Thứ tự: `asc`, `desc` |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "folders": [
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "name": "Báo cáo tháng 3",
        "description": "Thư mục chứa các báo cáo tháng 3/2023",
        "path": "/Phòng ban A/Báo cáo tháng 3",
        "parentFolderId": "456e7890-e89b-12d3-a456-************",
        "ownerId": "user123-456-789",
        "ownerEmail": "<EMAIL>",
        "isPublic": false,
        "tags": ["báo-cáo", "tháng-3", "2023"],
        "createdAt": "2023-03-22T10:30:00Z",
        "updatedAt": "2023-03-22T10:30:00Z",
        "itemCount": 15,
        "totalSize": 52428800,
        "formattedSize": "50 MB",
        "version": 3,
        "hasSubfolders": true,
        "isShared": false,
        "permissions": {
          "canRead": true,
          "canWrite": true,
          "canDelete": true,
          "canShare": true
        }
      }
    ],
    "totalCount": 45,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  }
}
```

#### Ví dụ sử dụng

```bash
# Lấy tất cả thư mục root
GET /api/v1/folders/my-folders

# Thư mục con của folder cụ thể
GET /api/v1/folders/my-folders?parentFolderId=456e7890-e89b-12d3-a456-************

# Tìm kiếm thư mục báo cáo
GET /api/v1/folders/my-folders?searchTerm=báo%20cáo&folderType=public

# Lọc theo khoảng thời gian
GET /api/v1/folders/my-folders?createdAfter=2023-03-01&createdBefore=2023-03-31

# Bao gồm thư mục shared
GET /api/v1/folders/my-folders?includeShared=true&sortBy=name&sortOrder=asc
```

---

## 3. Lấy thông tin chi tiết thư mục

### `GET /api/v1/folders/{folderId}`

Lấy thông tin chi tiết của một thư mục cụ thể.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `folderId` | UUID | Có | ID của thư mục |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Báo cáo tháng 3",
    "description": "Thư mục chứa các báo cáo tháng 3/2023",
    "path": "/Phòng ban A/Báo cáo tháng 3",
    "parentFolderId": "456e7890-e89b-12d3-a456-************",
    "ownerId": "user123-456-789",
    "ownerEmail": "<EMAIL>",
    "isPublic": false,
    "tags": ["báo-cáo", "tháng-3", "2023"],
    "createdAt": "2023-03-22T10:30:00Z",
    "updatedAt": "2023-03-22T10:30:00Z",
    "deletedAt": null,
    "isDeleted": false,
    "itemCount": 15,
    "fileCount": 12,
    "subfolderCount": 3,
    "totalSize": 52428800,
    "formattedSize": "50 MB",
    "version": 3,
    "breadcrumb": [
      {
        "id": "root",
        "name": "Root",
        "path": "/"
      },
      {
        "id": "456e7890-e89b-12d3-a456-************",
        "name": "Phòng ban A",
        "path": "/Phòng ban A"
      },
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "name": "Báo cáo tháng 3",
        "path": "/Phòng ban A/Báo cáo tháng 3"
      }
    ],
    "permissions": {
      "canRead": true,
      "canWrite": true,
      "canDelete": true,
      "canShare": true,
      "canManagePermissions": true
    },
    "shares": [
      {
        "id": "share123",
        "shareToken": "abc123xyz",
        "shareType": "Public",
        "expiresAt": "2023-04-22T10:30:00Z",
        "isActive": true
      }
    ]
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Lấy thông tin thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền xem thư mục
- `404 Not Found`: Thư mục không tồn tại

---

## 4. Lấy nội dung thư mục

### `GET /api/v1/folders/{folderId}/contents`

Lấy danh sách files và subfolders trong thư mục.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `folderId` | UUID | Có | ID của thư mục |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mặc định | Mô tả |
|---------|------|----------|----------|-------|
| `page` | integer | Không | 1 | Số trang |
| `pageSize` | integer | Không | 50 | Số items/trang |
| `itemType` | string | Không | - | Lọc: `files`, `folders`, `all` |
| `searchTerm` | string | Không | - | Tìm kiếm theo tên |
| `sortBy` | string | Không | `name` | Sắp xếp: `name`, `size`, `createdAt`, `type` |
| `sortOrder` | string | Không | `asc` | Thứ tự: `asc`, `desc` |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "folderInfo": {
      "id": "123e4567-e89b-12d3-a456-************",
      "name": "Báo cáo tháng 3",
      "path": "/Phòng ban A/Báo cáo tháng 3"
    },
    "items": [
      {
        "id": "file123",
        "name": "report-march.pdf",
        "type": "file",
        "mimeType": "application/pdf",
        "size": 2048576,
        "formattedSize": "2 MB",
        "createdAt": "2023-03-22T10:30:00Z",
        "updatedAt": "2023-03-22T11:00:00Z",
        "downloadUrl": "/api/v1/files/file123/download",
        "thumbnailUrl": "/api/v1/files/file123/thumbnail"
      },
      {
        "id": "folder456",
        "name": "Sub Reports",
        "type": "folder",
        "itemCount": 8,
        "totalSize": 15728640,
        "formattedSize": "15 MB",
        "createdAt": "2023-03-22T09:00:00Z",
        "updatedAt": "2023-03-22T12:00:00Z",
        "hasSubfolders": true
      }
    ],
    "totalCount": 25,
    "fileCount": 18,
    "folderCount": 7,
    "page": 1,
    "pageSize": 50,
    "totalPages": 1
  }
}
```

#### Ví dụ sử dụng

```bash
# Lấy tất cả nội dung
GET /api/v1/folders/123e4567-e89b-12d3-a456-************/contents

# Chỉ lấy files
GET /api/v1/folders/123e4567-e89b-12d3-a456-************/contents?itemType=files

# Tìm kiếm và sắp xếp
GET /api/v1/folders/123e4567-e89b-12d3-a456-************/contents?searchTerm=report&sortBy=size&sortOrder=desc
```

---

## 5. Cập nhật thông tin thư mục

### `PUT /api/v1/folders/{folderId}`

Cập nhật metadata của thư mục.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `folderId` | UUID | Có | ID của thư mục |

#### Request Body

```json
{
  "name": "Báo cáo Q1 2023",
  "description": "Thư mục chứa báo cáo quý 1/2023 đã cập nhật",
  "isPublic": true,
  "tags": ["báo-cáo", "q1", "2023", "updated"]
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `name` | string | Không | Tên mới |
| `description` | string | Không | Mô tả mới |
| `isPublic` | boolean | Không | Trạng thái công khai |
| `tags` | string[] | Không | Tags mới |

#### Response Body

```json
{
  "success": true,
  "message": "Folder updated successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Báo cáo Q1 2023",
    "description": "Thư mục chứa báo cáo quý 1/2023 đã cập nhật",
    "isPublic": true,
    "tags": ["báo-cáo", "q1", "2023", "updated"],
    "updatedAt": "2023-03-22T13:00:00Z",
    "version": 4
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Cập nhật thành công

**❌ Lỗi:**
- `400 Bad Request`: Dữ liệu không hợp lệ, tên trùng
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền chỉnh sửa
- `404 Not Found`: Thư mục không tồn tại
- `409 Conflict`: Tên thư mục đã tồn tại

---

## 6. Di chuyển thư mục

### `PUT /api/v1/folders/{folderId}/move`

Di chuyển thư mục sang vị trí khác trong cây thư mục.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `folderId` | UUID | Có | ID của thư mục |

#### Request Body

```json
{
  "newParentFolderId": "target-folder-id-789",
  "newName": "Báo cáo Q1 2023 - Moved"
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `newParentFolderId` | UUID | Có | ID thư mục đích (null = root) |
| `newName` | string | Không | Tên mới (nếu cần đổi tên) |

#### Response Body

```json
{
  "success": true,
  "message": "Folder moved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "Báo cáo Q1 2023 - Moved",
    "oldPath": "/Phòng ban A/Báo cáo tháng 3",
    "newPath": "/Archive/Báo cáo Q1 2023 - Moved",
    "parentFolderId": "target-folder-id-789",
    "updatedAt": "2023-03-22T14:00:00Z",
    "version": 5
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Di chuyển thành công

**❌ Lỗi:**
- `400 Bad Request`: Không thể di chuyển vào chính nó hoặc thư mục con
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền di chuyển hoặc ghi vào thư mục đích
- `404 Not Found`: Thư mục nguồn hoặc đích không tồn tại
- `409 Conflict`: Thư mục cùng tên đã tồn tại trong đích

---

## 7. Xóa thư mục

### `DELETE /api/v1/folders/{folderId}`

Xóa thư mục (soft delete hoặc permanent delete).

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `folderId` | UUID | Có | ID của thư mục |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mặc định | Mô tả |
|---------|------|----------|----------|-------|
| `force` | boolean | Không | false | Xóa cả khi có nội dung |
| `permanent` | boolean | Không | false | Xóa vĩnh viễn |

#### Response Body

**Soft delete:**
```json
{
  "success": true,
  "message": "Folder moved to recycle bin successfully",
  "data": true
}
```

**Permanent delete:**
```json
{
  "success": true,
  "message": "Folder permanently deleted successfully (storage files preserved)",
  "data": true
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Xóa thành công

**❌ Lỗi:**
- `400 Bad Request`: Thư mục không rỗng (khi force=false)
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền xóa
- `404 Not Found`: Thư mục không tồn tại
- `409 Conflict`: Thư mục có nội dung và force=false

---

## 8. Xóa hàng loạt thư mục

### `DELETE /api/v1/folders/bulk`

Xóa nhiều thư mục cùng lúc.

#### Request Body

```json
{
  "folderIds": [
    "123e4567-e89b-12d3-a456-************",
    "456e7890-e89b-12d3-a456-************",
    "789e1234-e89b-12d3-a456-************"
  ],
  "force": true,
  "permanent": false
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `folderIds` | UUID[] | Có | Danh sách ID thư mục cần xóa |
| `force` | boolean | Không | Xóa cả khi có nội dung |
| `permanent` | boolean | Không | Xóa vĩnh viễn |

#### Response Body

```json
{
  "success": true,
  "message": "Bulk delete completed",
  "data": {
    "totalRequested": 3,
    "successCount": 2,
    "failedCount": 1,
    "results": [
      {
        "folderId": "123e4567-e89b-12d3-a456-************",
        "success": true,
        "message": "Folder deleted successfully"
      },
      {
        "folderId": "456e7890-e89b-12d3-a456-************",
        "success": true,
        "message": "Folder deleted successfully"
      },
      {
        "folderId": "789e1234-e89b-12d3-a456-************",
        "success": false,
        "message": "Permission denied",
        "errorCode": "FORBIDDEN"
      }
    ]
  }
}
```

---

## 9. Thống kê thư mục

### `GET /api/v1/folders/statistics`

Lấy thống kê tổng quan về thư mục của người dùng.

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `parentFolderId` | UUID | Không | Thống kê cho thư mục cụ thể |
| `includeSubfolders` | boolean | Không | Bao gồm thư mục con |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "totalFolders": 156,
    "totalFiles": 2847,
    "totalSize": 10737418240,
    "formattedTotalSize": "10 GB",
    "publicFolders": 23,
    "privateFolders": 133,
    "sharedFolders": 45,
    "emptyFolders": 12,
    "largestFolder": {
      "id": "large-folder-id",
      "name": "Video Archive",
      "size": 2147483648,
      "formattedSize": "2 GB"
    },
    "recentActivity": {
      "foldersCreatedToday": 3,
      "foldersModifiedToday": 8,
      "foldersDeletedToday": 1
    },
    "topLevelFolders": 28,
    "maxDepth": 8,
    "averageItemsPerFolder": 18.2
  }
}
```

---

## Workflow sử dụng

### 1. Tạo và quản lý thư mục
```
1. POST /folders → Tạo thư mục mới
2. GET /folders/my-folders → Xem danh sách
3. PUT /folders/{id} → Cập nhật thông tin
4. PUT /folders/{id}/move → Di chuyển thư mục
```

### 2. Duyệt nội dung thư mục
```
1. GET /folders/{id} → Xem thông tin chi tiết
2. GET /folders/{id}/contents → Xem nội dung
3. GET /folders/statistics → Thống kê tổng quan
```

### 3. Xóa thư mục
```
1. DELETE /folders/{id} → Xóa đơn lẻ
2. DELETE /folders/bulk → Xóa hàng loạt
3. GET /recycle-bin → Xem thư mục đã xóa
```

## Error Codes tổng hợp

| Code | HTTP Status | Mô tả |
|------|-------------|-------|
| `FOLDER_NOT_FOUND` | 404 | Thư mục không tồn tại |
| `PERMISSION_DENIED` | 403 | Không có quyền truy cập |
| `DUPLICATE_NAME` | 409 | Tên thư mục đã tồn tại |
| `FOLDER_NOT_EMPTY` | 409 | Thư mục có nội dung (khi force=false) |
| `INVALID_PARENT` | 400 | Thư mục cha không hợp lệ |
| `CIRCULAR_REFERENCE` | 400 | Tạo vòng lặp trong cây thư mục |
| `MAX_DEPTH_EXCEEDED` | 400 | Vượt quá độ sâu tối đa (20 cấp) |
| `NAME_TOO_LONG` | 400 | Tên thư mục quá dài (>255 ký tự) |

## Giới hạn hệ thống

- **Độ sâu tối đa:** 20 cấp thư mục
- **Tên thư mục:** 1-255 ký tự
- **Số thư mục con:** Không giới hạn
- **Mô tả:** Tối đa 1000 ký tự
- **Tags:** Tối đa 20 tags, mỗi tag 50 ký tự
