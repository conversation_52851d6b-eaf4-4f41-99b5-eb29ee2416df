"use client";

import { useEffect } from "react";
import { UserManager, UserManagerSettings } from "oidc-client-ts";
import { oidcConfig } from "@/lib/oidcConfig";

export default function SilentCallbackPage() {
  useEffect(() => {
    const handleSilentCallback = async () => {
      try {
        const userManager = new UserManager(oidcConfig as UserManagerSettings);
        await userManager.signinSilentCallback();
        console.log("Silent token renewal successful");
      } catch (err) {
        console.error("Silent token renewal error:", err);
      }
    };

    handleSilentCallback();
  }, []);

  // This is an invisible page, so we don't return any UI
  return null;
}
