import { ApiClient } from '../core/apiClient';
import { ApiError } from '../core/apiClient';
import {
  UserProfile,
  UpdateUserProfileRequest,
  UpdateUserProfileResponse
} from '@/types/user';

/**
 * User Service for managing user information and profile operations
 *
 * Provides methods for:
 * - Getting current user information
 * - Updating user profile
 * - Managing user preferences
 */
export class UserService {
  private client: ApiClient;
  private getTokenFn?: () => Promise<string | null>;

  constructor(apiClient: ApiClient, getAccessToken?: () => Promise<string | null>) {
    this.client = apiClient;
    this.getTokenFn = getAccessToken;
  }

  /**
   * Get current user information from Identity Server OIDC userinfo endpoint
   * @returns Current user profile information
   *
   * @example
   * ```typescript
   * const userService = new UserService(apiClient);
   * const userInfo = await userService.getCurrentUser();
   * console.log('User:', userInfo.name, userInfo.email);
   * ```
   */
  async getCurrentUser(): Promise<UserProfile> {
    try {
      // Call Identity Server OIDC userinfo endpoint directly
      const token = await this.getAccessToken();
      const response = await fetch('https://sso.veasy.vn/connect/userinfo', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const userInfo: UserProfile = await response.json();
      return userInfo;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        'Failed to retrieve user information from Identity Server',
        (error as any).statusCode || 500,
        'USER_INFO_ERROR',
        (error as any).correlationId
      );
    }
  }

  /**
   * Get access token from current user session
   * @private
   */
  private async getAccessToken(): Promise<string> {
    // Use the provided token function if available
    if (this.getTokenFn) {
      const token = await this.getTokenFn();
      if (token) {
        return token;
      }
    }

    // Fallback: try to get token from storage
    if (typeof window !== 'undefined') {
      const token = sessionStorage.getItem('oidc.access_token') ||
                   localStorage.getItem('oidc.access_token');
      if (token) {
        return token;
      }
    }

    throw new Error('No access token available');
  }

  /**
   * Update current user profile information
   * @param updateData User profile data to update
   * @returns Updated user profile
   *
   * @example
   * ```typescript
   * const userService = new UserService(apiClient);
   * const updatedUser = await userService.updateProfile({
   *   name: 'John Doe',
   *   phone: '+1234567890',
   *   department: 'Engineering'
   * });
   * ```
   */
  async updateProfile(updateData: UpdateUserProfileRequest): Promise<UserProfile> {
    try {
      const response = await this.client.put<UpdateUserProfileResponse>('/auth/me', updateData);

      if (!response.success) {
        throw new ApiError(
          response.message || 'Failed to update user profile',
          400,
          'USER_UPDATE_FAILED'
        );
      }

      return response.data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        'Failed to update user profile',
        (error as any).statusCode || 500,
        'USER_UPDATE_ERROR',
        (error as any).correlationId
      );
    }
  }

  /**
   * Upload user avatar
   * @param file Avatar image file
   * @returns Updated user profile with new avatar URL
   *
   * @example
   * ```typescript
   * const userService = new UserService(apiClient);
   * const fileInput = document.getElementById('avatar') as HTMLInputElement;
   * const file = fileInput.files[0];
   * const updatedUser = await userService.uploadAvatar(file);
   * ```
   */
  async uploadAvatar(file: File): Promise<UserProfile> {
    try {
      const formData = new FormData();
      formData.append('avatar', file);

      const response = await this.client.post<UpdateUserProfileResponse>('/auth/me/avatar', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      if (!response.success) {
        throw new ApiError(
          response.message || 'Failed to upload avatar',
          400,
          'AVATAR_UPLOAD_FAILED'
        );
      }

      return response.data;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      throw new ApiError(
        'Failed to upload avatar',
        (error as any).statusCode || 500,
        'AVATAR_UPLOAD_ERROR',
        (error as any).correlationId
      );
    }
  }

  /**
   * Get user activity log
   * @param limit Number of activities to retrieve (default: 50)
   * @param offset Offset for pagination (default: 0)
   * @returns User activity history
   */
  async getUserActivity(limit: number = 50, offset: number = 0): Promise<{
    activities: Array<{
      id: string;
      action: string;
      description: string;
      timestamp: string;
      ipAddress?: string;
      userAgent?: string;
    }>;
    total: number;
    hasMore: boolean;
  }> {
    try {
      const params = { limit, offset };
      return await this.client.get('/auth/me/activity', { params });
    } catch (error) {
      throw new ApiError(
        'Failed to retrieve user activity',
        (error as any).statusCode || 500,
        'USER_ACTIVITY_ERROR',
        (error as any).correlationId
      );
    }
  }

  /**
   * Change user password
   * @param currentPassword Current password
   * @param newPassword New password
   * @returns Success response
   */
  async changePassword(currentPassword: string, newPassword: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.client.post('/auth/me/change-password', {
        currentPassword,
        newPassword
      });

      return response as { success: boolean; message: string };
    } catch (error) {
      throw new ApiError(
        'Failed to change password',
        (error as any).statusCode || 500,
        'PASSWORD_CHANGE_ERROR',
        (error as any).correlationId
      );
    }
  }

  /**
   * Delete user account
   * @param password Current password for confirmation
   * @returns Success response
   */
  async deleteAccount(password: string): Promise<{ success: boolean; message: string }> {
    try {
      const response = await this.client.delete('/auth/me', {
        data: { password }
      });

      return response as { success: boolean; message: string };
    } catch (error) {
      throw new ApiError(
        'Failed to delete account',
        (error as any).statusCode || 500,
        'ACCOUNT_DELETE_ERROR',
        (error as any).correlationId
      );
    }
  }
}
