"use client";

import React, { useState, useEffect } from "react";
import { Modal } from "@/components/ui/Modal";
import {
  FolderIcon,
  CloudArrowUpIcon,
  DocumentIcon,
  ExclamationTriangleIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";

interface FolderManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: "createFolder" | "uploadFiles";
  onCreateFolder?: (name: string) => void;
  onUploadFiles?: (files: File[]) => void;
}

export function FolderManagementModal({
  isOpen,
  onClose,
  mode,
  onCreateFolder,
  onUploadFiles,
}: FolderManagementModalProps) {
  const [folderName, setFolderName] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setFolderName("");
      setSelectedFiles([]);
      setErrors({});
      setIsSubmitting(false);
    }
  }, [isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (mode === "createFolder") {
      if (!folderName.trim()) {
        newErrors.folderName = "Tên thư mục là bắt buộc";
      } else if (folderName.trim().length < 2) {
        newErrors.folderName = "Tên thư mục phải có ít nhất 2 ký tự";
      }
    } else if (mode === "uploadFiles") {
      if (selectedFiles.length === 0) {
        newErrors.files = "Vui lòng chọn ít nhất một tệp";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (mode === "createFolder" && onCreateFolder) {
        onCreateFolder(folderName.trim());
      } else if (mode === "uploadFiles" && onUploadFiles) {
        onUploadFiles(selectedFiles);
      }

      onClose();
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setSelectedFiles(files);
    if (files.length > 0) {
      setErrors((prev) => ({ ...prev, files: "" }));
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    const files = Array.from(e.dataTransfer.files);
    setSelectedFiles(files);
    if (files.length > 0) {
      setErrors((prev) => ({ ...prev, files: "" }));
    }
  };

  const removeFile = (index: number) => {
    setSelectedFiles((files) => files.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const getModalTitle = () => {
    switch (mode) {
      case "createFolder":
        return "Tạo thư mục mới";
      case "uploadFiles":
        return "Tải lên tệp tin";
      default:
        return "Quản lý thư mục";
    }
  };

  const getModalIcon = () => {
    switch (mode) {
      case "createFolder":
        return FolderIcon;
      case "uploadFiles":
        return CloudArrowUpIcon;
      default:
        return FolderIcon;
    }
  };

  const ModalIcon = getModalIcon();

  return (
    <Modal isOpen={isOpen} onClose={onClose} title={getModalTitle()} size="lg">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Icon */}
        <div className="flex justify-center">
          <div className="p-3 bg-indigo-100 rounded-full">
            <ModalIcon className="h-8 w-8 text-indigo-600" />
          </div>
        </div>

        {mode === "createFolder" ? (
          /* Create Folder Form */
          <div className="space-y-4">
            <div>
              <label
                htmlFor="folderName"
                className="block text-sm font-medium text-gray-700 mb-2"
              >
                Tên thư mục *
              </label>
              <input
                type="text"
                id="folderName"
                value={folderName}
                onChange={(e) => {
                  setFolderName(e.target.value);
                  if (errors.folderName) {
                    setErrors((prev) => ({ ...prev, folderName: "" }));
                  }
                }}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors ${
                  errors.folderName
                    ? "border-red-300 bg-red-50"
                    : "border-gray-300"
                }`}
                placeholder="Nhập tên thư mục"
              />
              {errors.folderName && (
                <p className="mt-1 text-sm text-red-600 flex items-center">
                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                  {errors.folderName}
                </p>
              )}
            </div>
          </div>
        ) : (
          /* Upload Files Form */
          <div className="space-y-4">
            {/* File Drop Zone */}
            <div
              className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
                dragActive
                  ? "border-indigo-500 bg-indigo-50"
                  : errors.files
                  ? "border-red-300 bg-red-50"
                  : "border-gray-300 hover:border-gray-400"
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <input
                type="file"
                multiple
                onChange={handleFileSelect}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
              />
              <div className="text-center">
                <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <p className="text-sm text-gray-600">
                    <span className="font-medium text-indigo-600 hover:text-indigo-500">
                      Nhấp để chọn tệp
                    </span>{" "}
                    hoặc kéo thả tệp vào đây
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Hỗ trợ: PDF, DOC, DOCX, TXT, JPG, PNG (tối đa 10MB mỗi tệp)
                  </p>
                </div>
              </div>
            </div>

            {errors.files && (
              <p className="text-sm text-red-600 flex items-center">
                <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                {errors.files}
              </p>
            )}

            {/* Selected Files List */}
            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">
                  Tệp đã chọn ({selectedFiles.length})
                </h4>
                <div className="max-h-40 overflow-y-auto space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-2">
                        <DocumentIcon className="h-5 w-5 text-gray-400" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {file.name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {formatFileSize(file.size)}
                          </p>
                        </div>
                      </div>
                      <button
                        type="button"
                        onClick={() => removeFile(index)}
                        className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      >
                        <XMarkIcon className="h-4 w-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Hủy
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>
                  {mode === "createFolder" ? "Đang tạo..." : "Đang tải lên..."}
                </span>
              </>
            ) : (
              <>
                <ModalIcon className="h-4 w-4" />
                <span>
                  {mode === "createFolder" ? "Tạo thư mục" : "Tải lên"}
                </span>
              </>
            )}
          </button>
        </div>
      </form>
    </Modal>
  );
}
