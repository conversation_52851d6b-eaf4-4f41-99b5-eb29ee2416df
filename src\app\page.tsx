"use client";

import React, { useState, useEffect } from "react";
import { UploadZone } from "@/components/ui/UploadZone";
import { FileList } from "@/components/ui/FileList";
import { FileSystemItem } from "@/types";
import {
  DocumentIcon,
  EyeIcon,
  ClockIcon,
  CogIcon,
} from "@heroicons/react/24/outline";
import { FileService } from "@/feature/file-manager/file-service";

export default function Dashboard() {
  const [files, setFiles] = useState<FileSystemItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadFiles = async () => {
      try {
        setIsLoading(true);
        const result = await FileService.getFiles();
        // Combine files and folders into one array
        const combinedFiles = [
          ...result.folders.map((folder) => ({
            ...folder,
            size: undefined,
          })),
          ...result.files,
        ];
        setFiles(combinedFiles);
        setError(null);
      } catch (err) {
        console.error("Error loading files:", err);
        setError("Failed to load files. Please try again.");
        // Use mock data as fallback if API fails
        setFiles([
          {
            id: "1",
            name: "document1.pdf",
            type: "file",
            size: 1024000,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: "2",
            name: "document2.pdf",
            type: "file",
            size: 2048000,
            createdAt: new Date(),
            updatedAt: new Date(),
          },
          {
            id: "3",
            name: "folder1",
            type: "folder",
            createdAt: new Date(),
            updatedAt: new Date(),
          },
        ]);
      } finally {
        setIsLoading(false);
      }
    };

    loadFiles();
  }, []);

  const stats = [
    {
      name: "Total Files",
      value: files.filter((f) => f.type === "file").length,
      icon: DocumentIcon,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      name: "Folders",
      value: files.filter((f) => f.type === "folder").length,
      icon: CogIcon,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      name: "Recent Views",
      value: 12,
      icon: EyeIcon,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      name: "Active Jobs",
      value: 3,
      icon: ClockIcon,
      color: "text-orange-600",
      bgColor: "bg-orange-100",
    },
  ];

  const handleFilesSelected = async (files: File[]) => {
    try {
      // We'll process each file and upload it
      for (const file of files) {
        await FileService.uploadFile(file, undefined, (progress) => {
          console.log(`Upload progress for ${file.name}: ${progress}%`);
        });
      }

      // Refresh the file list after upload
      const result = await FileService.getFiles();
      const combinedFiles = [
        ...result.folders.map((folder) => ({
          ...folder,
          size: undefined,
        })),
        ...result.files,
      ];
      setFiles(combinedFiles);
    } catch (error) {
      console.error("Error uploading files:", error);
      setError("Upload failed. Please try again.");
    }
  };

  const handleFileSelect = (file: FileSystemItem) => {
    if (file.type === "file") {
      // Navigate to viewer for files
      window.location.href = `/viewer?fileId=${file.id}`;
    } else {
      // For folders, we could load their contents
      console.log("Selected folder:", file.id);
    }
  };

  const handleFileDelete = async (fileId: string) => {
    try {
      // Determine if it's a file or folder
      const item = files.find((f) => f.id === fileId);

      if (item?.type === "file") {
        await FileService.deleteFile(fileId);
      } else if (item?.type === "folder") {
        await FileService.deleteFolder(fileId);
      }

      // Update state by removing the deleted item
      setFiles(files.filter((f) => f.id !== fileId));
    } catch (error) {
      console.error("Error deleting item:", error);
      setError("Deletion failed. Please try again.");
    }
  };

  const handleFolderCreate = async (name: string) => {
    try {
      const newFolder = await FileService.createFolder({ name });
      setFiles([...files, { ...newFolder, type: "folder" }]);
    } catch (error) {
      console.error("Error creating folder:", error);
      setError("Failed to create folder. Please try again.");
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative mb-4">
          {error}
          <button
            className="absolute top-0 right-0 p-2"
            onClick={() => setError(null)}
          >
            <span className="sr-only">Close</span>
            <svg
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      )}

      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">
          Manage your PDF files and OCR operations
        </p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div
            key={stat.name}
            className="bg-white overflow-hidden shadow rounded-lg"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-md ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upload Zone */}
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Upload Files
          </h2>
          <UploadZone
            onFilesSelected={handleFilesSelected}
            accept="application/pdf"
            multiple={true}
          />
        </div>

        {/* Files List */}
        <div>
          <FileList
            files={files}
            onFileSelect={handleFileSelect}
            onFileDelete={handleFileDelete}
            onFolderCreate={handleFolderCreate}
          />
        </div>
      </div>
    </div>
  );
}
