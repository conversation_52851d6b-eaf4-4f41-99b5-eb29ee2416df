import { ApiClient } from '../core/apiClient';
import {
  FileDto,
  UploadOptions,
  MultiUploadOptions,
  MultiUploadResponse,
  FileUpdateData,
  FileCopyOptions,
  FileMoveOptions,
  DownloadOptions,
  PresignedUrlResponse,
  PermissionRequest,
  PermissionDto,
  ShareOptions,
  ShareDto,
  ShareAccessResponse,
  SortField,
  SortDirection,
  PaginationInfo,
  Permission,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export class FileService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Upload a single file
   * @param file File to upload
   * @param options Upload options
   * @returns Uploaded file information
   */
  async upload(file: File, options?: UploadOptions): Promise<FileDto> {
    // Input validation
    this.validateFile(file);
    this.validateUploadOptions(options);

    const formData = new FormData();
    formData.append('file', file);

    if (options?.parentFolderId) formData.append('parentFolderId', options.parentFolderId);
    if (options?.displayName) formData.append('displayName', options.displayName);
    if (options?.description) formData.append('description', options.description);
    if (options?.syncToGoogleDrive !== undefined) formData.append('syncToGoogleDrive', String(options.syncToGoogleDrive));
    if (options?.tags) formData.append('tags', options.tags.join(','));
    if (options?.overwriteExisting !== undefined) formData.append('overwriteExisting', String(options.overwriteExisting));
    if (options?.customMetadata) formData.append('customMetadata', JSON.stringify(options.customMetadata));

    try {
      return await this.client.post<FileDto>('/files/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    } catch (error) {
      throw this.handleUploadError(error as ApiError);
    }
  }

  /**
   * Upload multiple files
   * @param files Array of files to upload
   * @param options Upload options
   * @returns Information about the upload operation
   */
  async uploadMultiple(files: File[], options?: MultiUploadOptions): Promise<MultiUploadResponse> {
    // Input validation
    if (!files || files.length === 0) {
      throw new ValidationApiError('Files array cannot be empty', [
        { field: 'files', message: 'At least one file is required', code: 'REQUIRED' }
      ]);
    }

    files.forEach((file, index) => this.validateFile(file, `files[${index}]`));
    this.validateMultiUploadOptions(options);

    const formData = new FormData();

    files.forEach(file => {
      formData.append('files', file);
    });

    if (options?.parentFolderId) formData.append('parentFolderId', options.parentFolderId);
    if (options?.syncToGoogleDrive !== undefined) formData.append('syncToGoogleDrive', String(options.syncToGoogleDrive));
    if (options?.failOnError !== undefined) formData.append('failOnError', String(options.failOnError));
    if (options?.tags) formData.append('tags', options.tags.join(','));

    try {
      return await this.client.post<MultiUploadResponse>('/files/upload/multiple', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    } catch (error) {
      throw this.handleUploadError(error as ApiError);
    }
  }

  /**
   * Get file by ID
   * @param fileId ID of the file to retrieve
   * @returns File information
   */
  async getById(fileId: string): Promise<FileDto> {
    this.validateId(fileId, 'fileId');

    try {
      return await this.client.get<FileDto>(`/files/${fileId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'File');
    }
  }

  /**
   * List files with pagination and filtering
   * @param options Query options for listing files
   * @returns Paginated list of files
   */
  async list(options?: {
    page?: number;
    pageSize?: number;
    sortBy?: SortField;
    sortDirection?: SortDirection;
    parentFolderId?: string;
    tags?: string[];
    mimeType?: string;
  }): Promise<{ files: FileDto[]; pagination: PaginationInfo }> {
    const params: Record<string, any> = {};

    if (options?.page) params.page = options.page;
    if (options?.pageSize) params.pageSize = options.pageSize;
    if (options?.sortBy) params.sortBy = options.sortBy;
    if (options?.sortDirection) params.sortDirection = options.sortDirection;
    if (options?.parentFolderId) params.parentFolderId = options.parentFolderId;
    if (options?.tags) params.tags = options.tags.join(',');
    if (options?.mimeType) params.mimeType = options.mimeType;

    return await this.client.get<{ files: FileDto[]; pagination: PaginationInfo }>('/files', { params });
  }

  /**
   * Get download URL or download file directly
   * @param fileId ID of the file
   * @param options Download options
   * @returns Presigned URL object or Blob
   */
  async download(fileId: string, options?: DownloadOptions): Promise<PresignedUrlResponse | Blob> {
    this.validateId(fileId, 'fileId');

    const params: Record<string, any> = {};
    if (options?.presigned !== undefined) params.presigned = options.presigned;
    if (options?.expiration) params.expiration = options.expiration;

    try {
      if (options?.presigned) {
        return await this.client.get<PresignedUrlResponse>(`/files/${fileId}/download`, { params });
      } else {
        return await this.client.downloadFile(`/files/${fileId}/download`, { params });
      }
    } catch (error) {
      throw this.handleDownloadError(error as ApiError);
    }
  }

  /**
   * Get presigned download URL for a file
   * @param fileId ID of the file
   * @param expiration URL expiration time in seconds
   * @returns Object containing the presigned URL and expiry time
   */
  async getDownloadUrl(fileId: string, expiration: number = 3600): Promise<PresignedUrlResponse> {
    return this.download(fileId, { presigned: true, expiration }) as Promise<PresignedUrlResponse>;
  }

  /**
   * Download a file directly as Blob
   * @param fileId ID of the file to download
   * @returns Blob containing the file data
   */
  async downloadBlob(fileId: string): Promise<Blob> {
    return this.download(fileId, { presigned: false }) as Promise<Blob>;
  }

  /**
   * Update file metadata
   * @param fileId ID of the file to update
   * @param data Updated metadata
   * @returns Updated file information
   */
  async updateMetadata(fileId: string, data: FileUpdateData): Promise<FileDto> {
    this.validateId(fileId, 'fileId');
    this.validateUpdateData(data);

    try {
      return await this.client.put<FileDto>(`/files/${fileId}`, data);
    } catch (error) {
      throw this.handleUpdateError(error as ApiError);
    }
  }

  /**
   * Delete a file
   * @param fileId ID of the file to delete
   * @param permanent Whether to permanently delete the file
   */
  async delete(fileId: string, permanent: boolean = false): Promise<void> {
    this.validateId(fileId, 'fileId');

    try {
      await this.client.delete(`/files/${fileId}`, {
        params: { permanent }
      });
    } catch (error) {
      throw this.handleDeleteError(error as ApiError);
    }
  }

  /**
   * Copy a file
   * @param fileId ID of the file to copy
   * @param options Copy options
   * @returns Information about the copied file
   */
  async copy(fileId: string, options: FileCopyOptions): Promise<FileDto> {
    this.validateId(fileId, 'fileId');
    this.validateCopyOptions(options);

    try {
      return await this.client.post<FileDto>(`/files/${fileId}/copy`, options);
    } catch (error) {
      throw this.handleCopyError(error as ApiError);
    }
  }

  /**
   * Move a file to another folder
   * @param fileId ID of the file to move
   * @param options Move options
   */
  async move(fileId: string, options: FileMoveOptions): Promise<void> {
    this.validateId(fileId, 'fileId');
    this.validateMoveOptions(options);

    try {
      await this.client.post(`/files/${fileId}/move`, options);
    } catch (error) {
      throw this.handleMoveError(error as ApiError);
    }
  }

  // Permission management

  /**
   * Grant permission for a file
   * @param fileId ID of the file
   * @param permission Permission details
   * @returns ID of the created permission
   */
  async grantPermission(fileId: string, permission: PermissionRequest): Promise<string> {
    this.validateId(fileId, 'fileId');
    this.validatePermissionRequest(permission);

    try {
      return await this.client.post<string>(`/files/${fileId}/permissions`, permission);
    } catch (error) {
      throw this.handlePermissionError(error as ApiError);
    }
  }

  /**
   * List permissions for a file
   * @param fileId ID of the file
   * @returns List of permissions
   */
  async listPermissions(fileId: string): Promise<PermissionDto[]> {
    this.validateId(fileId, 'fileId');

    try {
      return await this.client.get<PermissionDto[]>(`/files/${fileId}/permissions`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'File');
    }
  }

  /**
   * Revoke a permission
   * @param permissionId ID of the permission to revoke
   */
  async revokePermission(permissionId: string): Promise<void> {
    this.validateId(permissionId, 'permissionId');

    try {
      await this.client.delete(`/files/permissions/${permissionId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Permission');
    }
  }

  // Sharing

  /**
   * Create a share link for a file
   * @param fileId ID of the file to share
   * @param options Share options
   * @returns Information about the created share
   */
  async createShareLink(fileId: string, options: ShareOptions): Promise<ShareDto> {
    this.validateId(fileId, 'fileId');
    this.validateShareOptions(options);

    try {
      return await this.client.post<ShareDto>(`/files/${fileId}/share`, options);
    } catch (error) {
      throw this.handleShareError(error as ApiError);
    }
  }

  /**
   * Access a shared file (public endpoint)
   * @param token Share token
   * @param password Password (for password-protected shares)
   * @returns Information about the shared file
   */
  async accessSharedFile(token: string, password?: string): Promise<ShareAccessResponse> {
    if (!token || typeof token !== 'string') {
      throw new ValidationApiError('Invalid share token', [
        { field: 'token', message: 'Share token is required', code: 'REQUIRED' }
      ]);
    }

    try {
      return await this.client.get<ShareAccessResponse>(`/shares/${token}`, {
        params: password ? { password } : undefined
      });
    } catch (error) {
      throw this.handleShareAccessError(error as ApiError);
    }
  }

  /**
   * Download shared file
   * @param token Share token
   * @param password Password (for password-protected shares)
   * @returns File blob
   */
  async downloadSharedFile(token: string, password?: string): Promise<Blob> {
    if (!token || typeof token !== 'string') {
      throw new ValidationApiError('Invalid share token', [
        { field: 'token', message: 'Share token is required', code: 'REQUIRED' }
      ]);
    }

    try {
      return await this.client.downloadFile(`/shares/${token}/download`, {
        params: password ? { password } : undefined
      });
    } catch (error) {
      throw this.handleShareAccessError(error as ApiError);
    }
  }

  /**
   * List shares for a file
   * @param fileId ID of the file
   * @returns List of active shares
   */
  async listShares(fileId: string): Promise<ShareDto[]> {
    this.validateId(fileId, 'fileId');

    try {
      return await this.client.get<ShareDto[]>(`/files/${fileId}/shares`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'File');
    }
  }

  /**
   * Delete/revoke a share
   * @param shareId ID of the share to revoke
   */
  async revokeShare(shareId: string): Promise<void> {
    this.validateId(shareId, 'shareId');

    try {
      await this.client.delete(`/shares/${shareId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Share');
    }
  }

  // Validation methods

  private validateFile(file: File, fieldName: string = 'file'): void {
    if (!file) {
      throw new ValidationApiError('File is required', [
        { field: fieldName, message: 'File is required', code: 'REQUIRED' }
      ]);
    }

    if (!(file instanceof File)) {
      throw new ValidationApiError('Invalid file object', [
        { field: fieldName, message: 'Must be a valid File object', code: 'INVALID_TYPE' }
      ]);
    }

    if (file.size === 0) {
      throw new ValidationApiError('Empty file not allowed', [
        { field: fieldName, message: 'File cannot be empty', code: 'EMPTY_FILE' }
      ]);
    }

    // Enhanced file size validation based on API documentation
    const maxSize = 100 * 1024 * 1024; // 100MB for regular upload
    if (file.size > maxSize) {
      throw new ValidationApiError('File too large', [
        { field: fieldName, message: `File size cannot exceed ${maxSize / 1024 / 1024}MB. Use chunked upload for larger files.`, code: ErrorCode.FILE_TOO_LARGE }
      ]);
    }

    // Enhanced file type validation based on security best practices
    this.validateFileType(file, fieldName);
  }

  /**
   * Enhanced file type validation
   * @param file File to validate
   * @param fieldName Field name for error reporting
   */
  private validateFileType(file: File, fieldName: string): void {
    // Dangerous file extensions that should be blocked
    const dangerousExtensions = [
      '.exe', '.bat', '.cmd', '.com', '.scr', '.vbs', '.js', '.jar',
      '.app', '.deb', '.pkg', '.dmg', '.msi', '.ps1', '.sh'
    ];

    const fileName = file.name.toLowerCase();
    const fileExtension = fileName.substring(fileName.lastIndexOf('.'));

    if (dangerousExtensions.includes(fileExtension)) {
      throw new ValidationApiError('File type not allowed', [
        { field: fieldName, message: `File type ${fileExtension} is not allowed for security reasons`, code: ErrorCode.INVALID_FILE_TYPE }
      ]);
    }

    // Validate MIME type matches extension for common types
    const mimeTypeValidation = this.validateMimeType(file.type, fileExtension);
    if (!mimeTypeValidation.isValid) {
      throw new ValidationApiError('File type mismatch', [
        { field: fieldName, message: mimeTypeValidation.message, code: ErrorCode.INVALID_FILE_TYPE }
      ]);
    }
  }

  /**
   * Validate MIME type against file extension
   * @param mimeType MIME type from file
   * @param extension File extension
   * @returns Validation result
   */
  private validateMimeType(mimeType: string, extension: string): { isValid: boolean; message: string } {
    const commonMimeTypes: Record<string, string[]> = {
      '.pdf': ['application/pdf'],
      '.doc': ['application/msword'],
      '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      '.xls': ['application/vnd.ms-excel'],
      '.xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
      '.ppt': ['application/vnd.ms-powerpoint'],
      '.pptx': ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
      '.jpg': ['image/jpeg'],
      '.jpeg': ['image/jpeg'],
      '.png': ['image/png'],
      '.gif': ['image/gif'],
      '.txt': ['text/plain'],
      '.csv': ['text/csv', 'application/csv'],
      '.zip': ['application/zip'],
      '.rar': ['application/x-rar-compressed'],
      '.mp4': ['video/mp4'],
      '.avi': ['video/x-msvideo'],
      '.mp3': ['audio/mpeg'],
      '.wav': ['audio/wav']
    };

    const expectedMimeTypes = commonMimeTypes[extension];
    if (expectedMimeTypes && !expectedMimeTypes.includes(mimeType)) {
      return {
        isValid: false,
        message: `File extension ${extension} does not match MIME type ${mimeType}`
      };
    }

    return { isValid: true, message: '' };
  }

  private validateId(id: string, fieldName: string): void {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationApiError(`Invalid ${fieldName}`, [
        { field: fieldName, message: `${fieldName} is required and must be a non-empty string`, code: 'REQUIRED' }
      ]);
    }
  }

  private validateUploadOptions(options?: UploadOptions): void {
    if (!options) return;

    if (options.tags && (!Array.isArray(options.tags) || options.tags.some(tag => typeof tag !== 'string'))) {
      throw new ValidationApiError('Invalid tags', [
        { field: 'tags', message: 'Tags must be an array of strings', code: 'INVALID_TYPE' }
      ]);
    }
  }

  private validateMultiUploadOptions(options?: MultiUploadOptions): void {
    if (!options) return;

    if (options.tags && (!Array.isArray(options.tags) || options.tags.some(tag => typeof tag !== 'string'))) {
      throw new ValidationApiError('Invalid tags', [
        { field: 'tags', message: 'Tags must be an array of strings', code: 'INVALID_TYPE' }
      ]);
    }
  }

  private validateUpdateData(data: FileUpdateData): void {
    if (!data || Object.keys(data).length === 0) {
      throw new ValidationApiError('Update data is required', [
        { field: 'data', message: 'At least one field must be provided for update', code: 'REQUIRED' }
      ]);
    }
  }

  private validateCopyOptions(options: FileCopyOptions): void {
    if (!options.targetFolderId) {
      throw new ValidationApiError('Target folder is required', [
        { field: 'targetFolderId', message: 'Target folder ID is required', code: 'REQUIRED' }
      ]);
    }
  }

  private validateMoveOptions(options: FileMoveOptions): void {
    if (!options.targetFolderId) {
      throw new ValidationApiError('Target folder is required', [
        { field: 'targetFolderId', message: 'Target folder ID is required', code: 'REQUIRED' }
      ]);
    }
  }

  private validatePermissionRequest(permission: PermissionRequest): void {
    if (!permission.userId && !permission.roleId) {
      throw new ValidationApiError('Permission target is required', [
        { field: 'permission', message: 'Either userId or roleId must be provided', code: 'REQUIRED' }
      ]);
    }

    if (!Object.values(Permission).includes(permission.permission)) {
      throw new ValidationApiError('Invalid permission type', [
        { field: 'permission', message: 'Invalid permission type', code: 'INVALID_PERMISSION_TYPE' }
      ]);
    }
  }

  private validateShareOptions(options: ShareOptions): void {
    if (options.shareType === 'Password' && !options.password) {
      throw new ValidationApiError('Password is required for password-protected shares', [
        { field: 'password', message: 'Password is required when shareType is Password', code: 'REQUIRED' }
      ]);
    }

    if (options.maxDownloads && options.maxDownloads < 1) {
      throw new ValidationApiError('Invalid maxDownloads', [
        { field: 'maxDownloads', message: 'maxDownloads must be greater than 0', code: 'INVALID_VALUE' }
      ]);
    }
  }

  // Error handling methods

  private handleUploadError(error: ApiError): ApiError {
    if (error.statusCode === 413) {
      return new ApiError('File too large', 413, ErrorCode.FILE_TOO_LARGE, error.correlationId);
    }
    if (error.statusCode === 409) {
      return new ApiError('File already exists', 409, ErrorCode.DUPLICATE_FILE_NAME, error.correlationId);
    }
    if (error.statusCode === 400 && error.isType(ErrorCode.INVALID_FILE_TYPE)) {
      return new ApiError('File type not allowed', 400, ErrorCode.INVALID_FILE_TYPE, error.correlationId);
    }
    return error;
  }

  private handleDownloadError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to download file', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'File');
  }

  private handleNotFoundError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }

  private handleUpdateError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to update file', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'File');
  }

  private handleDeleteError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to delete file', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'File');
  }

  private handleCopyError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to copy file', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleMoveError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to move file', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handlePermissionError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Permission already exists', 409, ErrorCode.PERMISSION_ALREADY_EXISTS, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to grant permission', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleShareError(error: ApiError): ApiError {
    if (error.statusCode === 429) {
      return new ApiError('Share limit exceeded', 429, ErrorCode.SHARE_LIMIT_EXCEEDED, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to share file', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleShareAccessError(error: ApiError): ApiError {
    if (error.statusCode === 401) {
      return new ApiError('Invalid share password', 401, ErrorCode.INVALID_SHARE_PASSWORD, error.correlationId);
    }
    if (error.statusCode === 410) {
      return new ApiError('Share has expired', 410, ErrorCode.SHARE_EXPIRED, error.correlationId);
    }
    if (error.statusCode === 429) {
      return new ApiError('Maximum downloads reached', 429, ErrorCode.MAX_DOWNLOADS_REACHED, error.correlationId);
    }
    return this.handleNotFoundError(error, 'Share');
  }
}
