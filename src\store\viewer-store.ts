import { create } from "zustand";

interface ViewerState {
  // Existing properties
  scale: number;
  rotation: number;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  error: string | null;

  // Split range properties
  splitRanges: SplitRange[];
  selectedRange: string | null;

  // Existing methods
  setScale: (scale: number) => void;
  setRotation: (rotation: number) => void;
  setCurrentPage: (page: number) => void;
  setTotalPages: (total: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // Split range methods
  addSplitRange: (range: Omit<SplitRange, "id">) => void;
  updateSplitRange: (id: string, updates: Partial<SplitRange>) => void;
  removeSplitRange: (id: string) => void;
  clearSplitRanges: () => void;
  setSelectedRange: (id: string | null) => void;
}

// Define SplitRange type
type SplitRange = {
  id: string;
  start: number;
  end: number;
  name: string;
  color: string;
};

export const useViewerStore = create<ViewerState>((set) => ({
  scale: 1,
  rotation: 0,
  currentPage: 1,
  totalPages: 1,
  isLoading: false,
  error: null,
  splitRanges: [],
  selectedRange: null,
  setScale: (scale) => set({ scale }),
  setRotation: (rotation) => set({ rotation }),
  setCurrentPage: (page) => set({ currentPage: page }),
  setTotalPages: (total) => set({ totalPages: total }),
  setLoading: (loading) => set({ isLoading: loading }),
  setError: (error) => set({ error }),
  addSplitRange: (range) =>
    set((state) => ({
      splitRanges: [
        ...state.splitRanges,
        { ...range, id: crypto.randomUUID() },
      ],
    })),
  updateSplitRange: (id, updates) =>
    set((state) => ({
      splitRanges: state.splitRanges.map((range) =>
        range.id === id ? { ...range, ...updates } : range
      ),
    })),
  removeSplitRange: (id) =>
    set((state) => ({
      splitRanges: state.splitRanges.filter((range) => range.id !== id),
    })),
  clearSplitRanges: () => set({ splitRanges: [] }),
  setSelectedRange: (id) => set({ selectedRange: id }),
}));
