import React from 'react';
import Link from 'next/link';
import { 
  UserGroupIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  ArrowLeftIcon
} from '@heroicons/react/24/outline';

export default function RolesPage() {
  // Mock data - replace with actual API call
  const roles = [
    {
      id: 1,
      name: 'Super Admin',
      description: 'Quyền cao nhất trong hệ thống, có thể thực hiện mọi thao tác',
      userCount: 1,
      permissions: ['read', 'create', 'update', 'delete', 'admin'],
      color: 'bg-red-100 text-red-800',
      createdAt: '2024-01-01'
    },
    {
      id: 2,
      name: 'Admin',
      description: 'Quản trị viên có quyền quản lý người dùng và nội dung',
      userCount: 2,
      permissions: ['read', 'create', 'update', 'delete'],
      color: 'bg-purple-100 text-purple-800',
      createdAt: '2024-01-01'
    },
    {
      id: 3,
      name: 'Editor',
      description: '<PERSON><PERSON> quyền tạo và chỉnh sửa nội dung',
      userCount: 3,
      permissions: ['read', 'create', 'update'],
      color: 'bg-blue-100 text-blue-800',
      createdAt: '2024-01-02'
    },
    {
      id: 4,
      name: 'Viewer',
      description: 'Chỉ có quyền xem nội dung',
      userCount: 5,
      permissions: ['read'],
      color: 'bg-gray-100 text-gray-800',
      createdAt: '2024-01-03'
    }
  ];

  const permissionLabels = {
    read: 'Xem',
    create: 'Tạo',
    update: 'Sửa',
    delete: 'Xóa',
    admin: 'Quản trị'
  };

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <Link
              href="/permission-management"
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md"
            >
              <ArrowLeftIcon className="h-5 w-5" />
            </Link>
            <UserGroupIcon className="h-8 w-8 text-gray-700 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900">Quản lý vai trò</h1>
          </div>
          <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <PlusIcon className="h-5 w-5 mr-2" />
            Tạo vai trò mới
          </button>
        </div>
        <p className="text-gray-600">
          Quản lý các vai trò và nhóm quyền trong hệ thống.
        </p>
      </div>

      {/* Roles Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {roles.map((role) => (
          <div key={role.id} className="bg-white rounded-lg border border-gray-200 shadow-sm">
            {/* Role Header */}
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{role.name}</h3>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${role.color}`}>
                    {role.userCount} người dùng
                  </span>
                </div>
                <div className="flex space-x-1">
                  <button className="p-1 text-blue-600 hover:bg-blue-50 rounded">
                    <EyeIcon className="h-4 w-4" />
                  </button>
                  <button className="p-1 text-green-600 hover:bg-green-50 rounded">
                    <PencilIcon className="h-4 w-4" />
                  </button>
                  <button className="p-1 text-red-600 hover:bg-red-50 rounded">
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
              <p className="text-sm text-gray-600">{role.description}</p>
            </div>

            {/* Permissions */}
            <div className="p-6">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Quyền hạn</h4>
              <div className="flex flex-wrap gap-2">
                {role.permissions.map((permission) => (
                  <span
                    key={permission}
                    className="inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full"
                  >
                    {permissionLabels[permission as keyof typeof permissionLabels]}
                  </span>
                ))}
              </div>
            </div>

            {/* Role Footer */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
              <div className="flex justify-between items-center text-xs text-gray-500">
                <span>Tạo: {role.createdAt}</span>
                <button className="text-blue-600 hover:text-blue-800 font-medium">
                  Chi tiết →
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Role Creation Form (Hidden by default) */}
      <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6" style={{ display: 'none' }}>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Tạo vai trò mới</h3>
        <form className="space-y-4">
          <div>
            <label htmlFor="roleName" className="block text-sm font-medium text-gray-700 mb-1">
              Tên vai trò *
            </label>
            <input
              type="text"
              id="roleName"
              name="roleName"
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Nhập tên vai trò"
            />
          </div>

          <div>
            <label htmlFor="roleDescription" className="block text-sm font-medium text-gray-700 mb-1">
              Mô tả
            </label>
            <textarea
              id="roleDescription"
              name="roleDescription"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Mô tả vai trò và trách nhiệm"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Quyền hạn *
            </label>
            <div className="space-y-2">
              {Object.entries(permissionLabels).map(([key, label]) => (
                <label key={key} className="flex items-center">
                  <input
                    type="checkbox"
                    name="permissions"
                    value={key}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{label}</span>
                </label>
              ))}
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <button
              type="button"
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors"
            >
              Hủy
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Tạo vai trò
            </button>
          </div>
        </form>
      </div>

      {/* Statistics */}
      <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-indigo-600">{roles.length}</div>
          <div className="text-sm text-gray-600">Tổng vai trò</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-green-600">
            {roles.reduce((sum, role) => sum + role.userCount, 0)}
          </div>
          <div className="text-sm text-gray-600">Người dùng được phân quyền</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-blue-600">
            {roles.filter(role => role.permissions.includes('admin')).length}
          </div>
          <div className="text-sm text-gray-600">Vai trò quản trị</div>
        </div>
        <div className="bg-white p-4 rounded-lg border border-gray-200">
          <div className="text-2xl font-bold text-purple-600">
            {Math.max(...roles.map(role => role.permissions.length))}
          </div>
          <div className="text-sm text-gray-600">Quyền tối đa/vai trò</div>
        </div>
      </div>
    </div>
  );
}
