"use client";

import React from "react";
import { useModal } from "@/components/providers/ModalProvider";
import {
  ShieldCheckIcon,
  EyeIcon,
  PlusIcon,
  PencilIcon,
  TrashIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";

export default function PermissionManagementPage() {
  const {
    openAddRoleModal,
    openEditRoleModal,
    openViewRoleModal,
    openDeleteModal,
  } = useModal();

  // 13 permissions in the system
  const allPermissions = [
    { id: "view_list_user", name: "View List User", category: "User" },
    { id: "add_user", name: "Add User", category: "User" },
    { id: "update_user", name: "Update User", category: "User" },
    { id: "view_user_detail", name: "View User Detail", category: "User" },
    { id: "view_list_role", name: "View List Role", category: "Role" },
    { id: "add_role", name: "Add Role", category: "Role" },
    { id: "update_role", name: "Update Role", category: "Role" },
    { id: "view_role_detail", name: "View Role Detail", category: "Role" },
    {
      id: "view_list_folder_file",
      name: "View List Folder/File",
      category: "Folder/File",
    },
    { id: "add_folder_file", name: "Add Folder/File", category: "Folder/File" },
    {
      id: "update_folder_file",
      name: "Update Folder/File",
      category: "Folder/File",
    },
    { id: "view_detail", name: "View Detail", category: "Folder/File" },
    { id: "detach_file", name: "Detach File", category: "Folder/File" },
  ];

  const permissionMatrix = [
    {
      id: "super_admin",
      role: "Super Admin",
      description: "Toàn quyền quản trị hệ thống",
      color: "bg-red-100 text-red-800 border-red-200",
      userCount: 2,
      permissions: {
        view_list_user: true,
        add_user: true,
        update_user: true,
        view_user_detail: true,
        view_list_role: true,
        add_role: true,
        update_role: true,
        view_role_detail: true,
        view_list_folder_file: true,
        add_folder_file: true,
        update_folder_file: true,
        view_detail: true,
        detach_file: true,
      },
    },
    {
      id: "admin",
      role: "Admin",
      description: "Quản trị viên với quyền hạn cao",
      color: "bg-purple-100 text-purple-800 border-purple-200",
      userCount: 5,
      permissions: {
        view_list_user: true,
        add_user: true,
        update_user: true,
        view_user_detail: true,
        view_list_role: true,
        add_role: false,
        update_role: false,
        view_role_detail: true,
        view_list_folder_file: true,
        add_folder_file: true,
        update_folder_file: true,
        view_detail: true,
        detach_file: true,
      },
    },
    {
      id: "editor",
      role: "Editor",
      description: "Biên tập viên có thể chỉnh sửa nội dung",
      color: "bg-blue-100 text-blue-800 border-blue-200",
      userCount: 12,
      permissions: {
        view_list_user: true,
        add_user: false,
        update_user: false,
        view_user_detail: true,
        view_list_role: true,
        add_role: false,
        update_role: false,
        view_role_detail: true,
        view_list_folder_file: true,
        add_folder_file: true,
        update_folder_file: true,
        view_detail: true,
        detach_file: false,
      },
    },
    {
      id: "viewer",
      role: "Viewer",
      description: "Người xem chỉ có quyền đọc",
      color: "bg-gray-100 text-gray-800 border-gray-200",
      userCount: 25,
      permissions: {
        view_list_user: true,
        add_user: false,
        update_user: false,
        view_user_detail: true,
        view_list_role: true,
        add_role: false,
        update_role: false,
        view_role_detail: true,
        view_list_folder_file: true,
        add_folder_file: false,
        update_folder_file: false,
        view_detail: true,
        detach_file: false,
      },
    },
  ];

  // Convert permissions to the format expected by the modal
  const convertToModalPermissions = () => {
    return allPermissions.map((permission) => ({
      id: permission.id,
      name: permission.name,
      description: `Quyền ${permission.name.toLowerCase()}`,
      category: permission.category.toLowerCase(),
      actions: ["read", "write", "delete"].filter((_, index) =>
        permission.name.includes("View")
          ? index === 0
          : permission.name.includes("Add")
          ? index === 1
          : permission.name.includes("Update")
          ? index === 1
          : permission.name.includes("Delete") ||
            permission.name.includes("Detach")
          ? index === 2
          : true
      ),
    }));
  };

  // Handler functions
  const handleAddRole = () => {
    openAddRoleModal(convertToModalPermissions());
  };

  const handleEditRole = (roleData: (typeof permissionMatrix)[number]) => {
    const roleForModal = {
      id: roleData.id,
      name: roleData.role,
      description: roleData.description,
      permissions: Object.entries(roleData.permissions)
        .filter(([_, hasPermission]) => hasPermission)
        .map(([permissionId, _]) => permissionId),
      color: roleData.color.includes("red")
        ? "red"
        : roleData.color.includes("purple")
        ? "purple"
        : roleData.color.includes("blue")
        ? "blue"
        : "gray",
      isActive: true,
      userCount: roleData.userCount,
    };
    openEditRoleModal(roleForModal, convertToModalPermissions());
  };

  const handleViewRole = (roleData: (typeof permissionMatrix)[number]) => {
    const roleForModal = {
      id: roleData.id,
      name: roleData.role,
      description: roleData.description,
      permissions: Object.entries(roleData.permissions)
        .filter(([_, hasPermission]) => hasPermission)
        .map(([permissionId, _]) => permissionId),
      color: roleData.color.includes("red")
        ? "red"
        : roleData.color.includes("purple")
        ? "purple"
        : roleData.color.includes("blue")
        ? "blue"
        : "gray",
      isActive: true,
      userCount: roleData.userCount,
    };
    openViewRoleModal(roleForModal, convertToModalPermissions());
  };

  const handleDeleteRole = (roleData: (typeof permissionMatrix)[number]) => {
    openDeleteModal({
      title: "Xác nhận xóa vai trò",
      message: `Bạn có chắc chắn muốn xóa vai trò "${roleData.role}"? Hành động này không thể hoàn tác.`,
      itemName: roleData.role,
      itemType: "vai trò",
      onConfirm: () => {
        console.log("Deleting role:", roleData.role);
        // Here you would typically call an API to delete the role
      },
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-8">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center mb-2">
                <div className="p-2 bg-indigo-100 rounded-lg mr-3">
                  <ShieldCheckIcon className="h-8 w-8 text-indigo-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Quản lý phân quyền
                  </h1>
                  <p className="text-gray-600 mt-1">
                    Quản lý vai trò, quyền hạn và bảo mật hệ thống
                  </p>
                </div>
              </div>
            </div>
            {/* <div className="flex space-x-3">
              <button
                onClick={handleAddRole}
                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Tạo vai trò
              </button>
            </div> */}
          </div>
        </div>
      </div>

      <div className="px-6 py-8">
        {/* Roles Management */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Roles List */}
          <div className="bg-white h-fit rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Danh sách vai trò
                </h3>
                <button
                  onClick={handleAddRole}
                  className="inline-flex items-center px-3 py-1.5 bg-indigo-600 text-white text-sm rounded-md hover:bg-indigo-700 transition-colors"
                >
                  <PlusIcon className="h-4 w-4 mr-1" />
                  Thêm vai trò
                </button>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                {permissionMatrix.map((role) => (
                  <div
                    key={role.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span
                          className={`inline-flex items-center px-3 py-1 text-xs font-semibold rounded-full ${role.color}`}
                        >
                          <UserGroupIcon className="h-3 w-3 mr-1" />
                          {role.role}
                        </span>
                        <div className="flex flex-col">
                          <div className="text-sm text-gray-900 font-medium">
                            {role.description}
                          </div>
                          <div className="text-xs text-gray-500">
                            {
                              Object.values(role.permissions).filter(Boolean)
                                .length
                            }
                            /{Object.keys(role.permissions).length} quyền •{" "}
                            {role.userCount} người dùng
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handleEditRole(role)}
                        className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded transition-colors"
                        title="Chỉnh sửa vai trò"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleViewRole(role)}
                        className="p-1.5 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded transition-colors"
                        title="Xem chi tiết"
                      >
                        <EyeIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteRole(role)}
                        className="p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded transition-colors"
                        title="Xóa vai trò"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Permission Matrix */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Ma trận quyền theo vai trò
                </h3>
                <div className="text-sm text-gray-500">
                  {permissionMatrix.length} vai trò • {allPermissions.length}{" "}
                  quyền
                </div>
              </div>
            </div>
            <div className="p-6">
              <div className="space-y-6">
                {permissionMatrix.map((item) => (
                  <div
                    key={item.id}
                    className="border border-gray-200 rounded-lg p-5 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-3">
                        <span
                          className={`inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full ${item.color}`}
                        >
                          <UserGroupIcon className="h-4 w-4 mr-1" />
                          {item.role}
                        </span>
                        <div className="text-sm text-gray-600">
                          {item.description}
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-gray-500">
                          {
                            Object.values(item.permissions).filter(Boolean)
                              .length
                          }
                          /13 quyền
                        </span>
                        <div className="flex items-center space-x-1">
                          <button
                            onClick={() => handleEditRole(item)}
                            className="p-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded transition-colors"
                            title="Chỉnh sửa"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleViewRole(item)}
                            className="p-1 text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded transition-colors"
                            title="Xem chi tiết"
                          >
                            <EyeIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>

                    {/* Permission Grid by Category */}
                    <div className="space-y-3">
                      {["User", "Role", "Folder/File"].map((category) => {
                        const categoryPermissions = allPermissions.filter(
                          (p) => p.category === category
                        );
                        return (
                          <div
                            key={category}
                            className="border border-gray-100 rounded-lg p-3"
                          >
                            <div className="text-xs font-medium text-gray-700 mb-2">
                              {category === "User"
                                ? "Quản lý người dùng"
                                : category === "Role"
                                ? "Quản lý vai trò"
                                : "Quản lý tệp tin"}
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                              {categoryPermissions.map((permission) => {
                                const hasPermission =
                                  item.permissions[
                                    permission.id as keyof typeof item.permissions
                                  ];
                                return (
                                  <div
                                    key={permission.id}
                                    className="flex items-center space-x-2"
                                  >
                                    <div
                                      className={`w-4 h-4 rounded-full flex items-center justify-center text-xs font-bold ${
                                        hasPermission
                                          ? "bg-green-100 text-green-600 border border-green-200"
                                          : "bg-red-100 text-red-600 border border-red-200"
                                      }`}
                                    >
                                      {hasPermission ? "✓" : "✗"}
                                    </div>
                                    <span
                                      className="text-xs text-gray-600 truncate"
                                      title={permission.name}
                                    >
                                      {permission.name.replace(
                                        /^(View|Add|Update|Delete)\s+/,
                                        ""
                                      )}
                                    </span>
                                  </div>
                                );
                              })}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
