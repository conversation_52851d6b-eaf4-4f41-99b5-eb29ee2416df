import { ApiClient } from './core/apiClient';
import { FileService } from './services/fileService';
import { FolderService } from './services/folderService';
import { SyncService } from './services/syncService';
import { ChunkedUploadService } from './services/chunkedUploadService';
import { HealthService } from './services/healthService';
import { UserService } from './services/userService';
import { ApiError, ValidationApiError } from './core/apiClient';
import { ApiConfig, HealthCheckResponse } from './types/interfaces';

/**
 * Main API client for VeasyFileManager
 *
 * Provides a comprehensive interface for interacting with the VeasyFileManager API,
 * including file management, folder operations, synchronization, and chunked uploads.
 *
 * @example
 * ```typescript
 * // Initialize with default configuration
 * const api = new VeasyFileManagerAPI();
 *
 * // Initialize with custom configuration
 * const api = new VeasyFileManagerAPI('/api/v1', 'your-jwt-token', {
 *   timeout: 60000,
 *   retryAttempts: 5,
 *   enableLogging: true
 * });
 *
 * // Upload a file
 * const fileInput = document.getElementById('file') as HTMLInputElement;
 * const file = fileInput.files[0];
 * const uploadedFile = await api.files.upload(file, {
 *   displayName: 'My Document',
 *   syncToGoogleDrive: true
 * });
 *
 * // Create a folder
 * const folder = await api.folders.create({
 *   name: 'My Folder',
 *   description: 'A folder for my documents'
 * });
 *
 * // Upload large file with chunked upload
 * const largeFile = fileInput.files[0];
 * const result = await api.chunkedUpload.uploadFile(largeFile, {
 *   chunkSize: 10 * 1024 * 1024, // 10MB chunks
 *   onProgress: (progress) => {
 *     console.log(`Upload progress: ${progress.progress}%`);
 *   }
 * });
 * ```
 */
export class VeasyFileManagerAPI {
  private apiClient: ApiClient;

  readonly files: FileService;
  readonly folders: FolderService;
  readonly sync: SyncService;
  readonly chunkedUpload: ChunkedUploadService;
  readonly health: HealthService;
  readonly user: UserService;

  /**
   * Create a new API client instance
   * @param baseURL Base URL for the API (default: '/api/v1')
   * @param token Optional JWT Bearer token for authentication
   * @param config Optional configuration for the API client
   */
  constructor(baseURL: string = '/api/v1', token?: string, config?: Partial<ApiConfig>) {
    this.apiClient = new ApiClient(baseURL, token, config);

    // Initialize services
    this.files = new FileService(this.apiClient);
    this.folders = new FolderService(this.apiClient);
    this.sync = new SyncService(this.apiClient);
    this.chunkedUpload = new ChunkedUploadService(this.apiClient);
    this.health = new HealthService(this.apiClient);
    this.user = new UserService(this.apiClient);
  }

  /**
   * Update the authentication token
   * @param token New JWT Bearer token
   *
   * @example
   * ```typescript
   * api.setToken('new-jwt-token');
   * ```
   */
  setToken(token: string): void {
    this.apiClient.setToken(token);
  }

  /**
   * Remove the authentication token
   *
   * @example
   * ```typescript
   * api.removeToken();
   * ```
   */
  removeToken(): void {
    this.apiClient.removeToken();
  }

  /**
   * Update the API client configuration
   * @param config Partial configuration to update
   *
   * @example
   * ```typescript
   * api.updateConfig({
   *   timeout: 120000, // 2 minutes
   *   retryAttempts: 5,
   *   enableLogging: false
   * });
   * ```
   */
  updateConfig(config: Partial<ApiConfig>): void {
    this.apiClient.updateConfig(config);
  }

  /**
   * Get current rate limit information
   * @returns Rate limit headers or null if not available
   *
   * @example
   * ```typescript
   * const rateLimit = api.getRateLimitInfo();
   * if (rateLimit) {
   *   console.log(`Remaining requests: ${rateLimit.remaining}/${rateLimit.limit}`);
   *   console.log(`Reset time: ${new Date(rateLimit.reset * 1000)}`);
   * }
   * ```
   */
  getRateLimitInfo() {
    return this.apiClient.getRateLimitHeaders();
  }

  /**
   * Check if the API is reachable and healthy
   * @returns Promise resolving to true if the API is healthy, false otherwise
   *
   * @example
   * ```typescript
   * const isHealthy = await api.healthCheck();
   * if (isHealthy) {
   *   console.log('API is operational');
   * } else {
   *   console.log('API is down or experiencing issues');
   * }
   * ```
   */
  async healthCheck(): Promise<boolean> {
    return this.health.healthCheck();
  }

  /**
   * Get detailed health information about the API and its services
   * @returns Detailed health check response
   *
   * @example
   * ```typescript
   * try {
   *   const health = await api.getDetailedHealth();
   *   console.log(`API Status: ${health.status}`);
   *   console.log(`Database: ${health.services.database}`);
   *   console.log(`Storage: ${health.services.storage}`);
   * } catch (error) {
   *   console.error('Failed to get health information:', error);
   * }
   * ```
   */
  async getDetailedHealth(): Promise<HealthCheckResponse> {
    return this.health.getDetailedHealth();
  }

  /**
   * Create a configured API instance with authentication
   * @param token JWT Bearer token
   * @param config Optional API configuration
   * @returns New API instance with authentication configured
   *
   * @example
   * ```typescript
   * const authenticatedApi = VeasyFileManagerAPI.withAuth('your-jwt-token', {
   *   timeout: 60000,
   *   enableLogging: true
   * });
   * ```
   */
  static withAuth(token: string, config?: Partial<ApiConfig>): VeasyFileManagerAPI {
    return new VeasyFileManagerAPI('/api/v1', token, config);
  }

  /**
   * Create a configured API instance for development
   * @param baseURL Development API base URL
   * @param token Optional JWT Bearer token
   * @returns New API instance configured for development
   *
   * @example
   * ```typescript
   * const devApi = VeasyFileManagerAPI.forDevelopment('http://localhost:3000/api/v1');
   * ```
   */
  static forDevelopment(baseURL: string, token?: string): VeasyFileManagerAPI {
    return new VeasyFileManagerAPI(baseURL, token, {
      enableLogging: true,
      retryAttempts: 1,
      timeout: 10000
    });
  }

  /**
   * Create a configured API instance for production
   * @param token JWT Bearer token
   * @param config Optional production-specific configuration
   * @returns New API instance configured for production
   *
   * @example
   * ```typescript
   * const prodApi = VeasyFileManagerAPI.forProduction('your-jwt-token', {
   *   timeout: 30000,
   *   retryAttempts: 3
   * });
   * ```
   */
  static forProduction(token: string, config?: Partial<ApiConfig>): VeasyFileManagerAPI {
    return new VeasyFileManagerAPI('/api/v1', token, {
      enableLogging: false,
      retryAttempts: 3,
      timeout: 30000,
      rateLimitRetry: true,
      ...config
    });
  }

  /**
   * Upload a file with progress tracking
   * @param file File to upload
   * @param options Upload options
   * @param onProgress Progress callback function
   * @returns Promise resolving to uploaded file information
   *
   * @example
   * ```typescript
   * const file = document.getElementById('file').files[0];
   *
   * const uploadedFile = await api.uploadWithProgress(
   *   file,
   *   { displayName: 'My Document' },
   *   (progress) => {
   *     console.log(`Upload: ${progress.loaded}/${progress.total} bytes`);
   *   }
   * );
   * ```
   */
  async uploadWithProgress(
    file: File,
    options?: any,
    onProgress?: (progressEvent: any) => void
  ): Promise<any> {
    const formData = new FormData();
    formData.append('file', file);

    if (options) {
      Object.keys(options).forEach(key => {
        if (options[key] !== undefined) {
          formData.append(key, typeof options[key] === 'object' ? JSON.stringify(options[key]) : String(options[key]));
        }
      });
    }

    try {
      return await this.apiClient.uploadWithProgress('/files/upload', formData, onProgress);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Batch operation helper - execute multiple operations with error handling
   * @param operations Array of operation functions
   * @param options Batch operation options
   * @returns Results of all operations with success/failure status
   *
   * @example
   * ```typescript
   * const results = await api.batchOperations([
   *   () => api.files.upload(file1),
   *   () => api.files.upload(file2),
   *   () => api.folders.create({ name: 'New Folder' })
   * ], {
   *   continueOnError: true,
   *   maxConcurrency: 3
   * });
   *
   * results.forEach((result, index) => {
   *   if (result.success) {
   *     console.log(`Operation ${index} succeeded:`, result.data);
   *   } else {
   *     console.error(`Operation ${index} failed:`, result.error);
   *   }
   * });
   * ```
   */
  async batchOperations<T>(
    operations: (() => Promise<T>)[],
    options: {
      continueOnError?: boolean;
      maxConcurrency?: number;
    } = {}
  ): Promise<Array<{ success: boolean; data?: T; error?: any; index: number }>> {
    const { continueOnError = true, maxConcurrency = 5 } = options;
    const results: Array<{ success: boolean; data?: T; error?: any; index: number }> = [];

    // Execute operations with concurrency limit
    for (let i = 0; i < operations.length; i += maxConcurrency) {
      const batch = operations.slice(i, i + maxConcurrency);
      const batchPromises = batch.map(async (operation, batchIndex) => {
        const operationIndex = i + batchIndex;
        try {
          const data = await operation();
          return { success: true, data, index: operationIndex };
        } catch (error) {
          if (!continueOnError) {
            throw error;
          }
          return { success: false, error, index: operationIndex };
        }
      });

      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        if (!continueOnError) {
          throw error;
        }
        // If we reach here, one operation failed and continueOnError is false
        // This shouldn't happen due to our try-catch in map, but just in case
        results.push({ success: false, error, index: i });
      }
    }

    return results.sort((a, b) => a.index - b.index);
  }

  /**
   * Get the underlying axios instance for advanced usage
   * @returns The axios instance used by the API client
   *
   * @example
   * ```typescript
   * const axiosInstance = api.getAxiosInstance();
   * // Use for custom requests or interceptors
   * ```
   */
  getAxiosInstance() {
    return this.apiClient.getAxiosInstance();
  }
}

// Re-export all types and interfaces
export * from './types/interfaces';

// Re-export error classes
export { ApiError, ValidationApiError } from './core/apiClient';

// Re-export services for advanced usage
export { FileService } from './services/fileService';
export { FolderService } from './services/folderService';
export { SyncService } from './services/syncService';
export { ChunkedUploadService } from './services/chunkedUploadService';
export { HealthService } from './services/healthService';
export { UserService } from './services/userService';
export { ApiClient } from './core/apiClient';

// Default export
export default VeasyFileManagerAPI;

/**
 * Utility function to check if an error is an API error
 * @param error Error to check
 * @returns True if the error is an ApiError
 *
 * @example
 * ```typescript
 * try {
 *   await api.files.upload(file);
 * } catch (error) {
 *   if (isApiError(error)) {
 *     console.log(`API Error: ${error.statusCode} - ${error.message}`);
 *     if (error.isType(ErrorCode.FILE_TOO_LARGE)) {
 *       console.log('File is too large for upload');
 *     }
 *   }
 * }
 * ```
 */
export function isApiError(error: any): error is ApiError {
  return error instanceof ApiError;
}

/**
 * Utility function to check if an error is a validation error
 * @param error Error to check
 * @returns True if the error is a ValidationApiError
 *
 * @example
 * ```typescript
 * try {
 *   await api.files.upload(null);
 * } catch (error) {
 *   if (isValidationError(error)) {
 *     console.log('Validation errors:');
 *     error.validationErrors.forEach(err => {
 *       console.log(`- ${err.field}: ${err.message}`);
 *     });
 *   }
 * }
 * ```
 */
export function isValidationError(error: any): error is ValidationApiError {
  return error instanceof ValidationApiError;
}

/**
 * Utility function to extract user-friendly error messages
 * @param error Error object
 * @returns User-friendly error message
 *
 * @example
 * ```typescript
 * try {
 *   await api.files.upload(file);
 * } catch (error) {
 *   const message = getUserFriendlyErrorMessage(error);
 *   alert(message);
 * }
 * ```
 */
export function getUserFriendlyErrorMessage(error: any): string {
  if (isValidationError(error)) {
    const fieldErrors = error.validationErrors.map(err => err.message).join(', ');
    return `Validation failed: ${fieldErrors}`;
  }

  if (isApiError(error)) {
    switch (error.statusCode) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in and try again.';
      case 403:
        return 'You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 413:
        return 'The file is too large to upload.';
      case 429:
        return 'Too many requests. Please wait a moment and try again.';
      case 500:
        return 'An internal server error occurred. Please try again later.';
      case 503:
        return 'The service is temporarily unavailable. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }

  if (error?.message) {
    return error.message;
  }

  return 'An unexpected error occurred. Please try again.';
}
