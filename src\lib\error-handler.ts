/**
 * Centralized error handling utilities
 * Provides consistent error handling across the application
 */

export enum ErrorType {
  AUTHENTICATION = "AUTHENTICATION",
  AUTHORIZATION = "AUTHORIZATION",
  NETWORK = "NETWORK",
  VALIDATION = "VALIDATION",
  FILE_UPLOAD = "FILE_UPLOAD",
  FILE_PROCESSING = "FILE_PROCESSING",
  UNKNOWN = "UNKNOWN",
}

export interface AppError {
  type: ErrorType;
  message: string;
  code?: string;
  details?: unknown;
  timestamp: Date;
}

export class AuthError extends Error {
  public readonly type = ErrorType.AUTHENTICATION;
  public readonly timestamp = new Date();

  constructor(
    message: string,
    public readonly code?: string,
    public readonly details?: unknown
  ) {
    super(message);
    this.name = "AuthError";
  }
}

export class NetworkError extends Error {
  public readonly type = ErrorType.NETWORK;
  public readonly timestamp = new Date();

  constructor(
    message: string,
    public readonly code?: string,
    public readonly details?: unknown
  ) {
    super(message);
    this.name = "NetworkError";
  }
}

export class ValidationError extends Error {
  public readonly type = ErrorType.VALIDATION;
  public readonly timestamp = new Date();

  constructor(
    message: string,
    public readonly field?: string,
    public readonly details?: unknown
  ) {
    super(message);
    this.name = "ValidationError";
  }
}

export class FileUploadError extends Error {
  public readonly type = ErrorType.FILE_UPLOAD;
  public readonly timestamp = new Date();

  constructor(
    message: string,
    public readonly fileName?: string,
    public readonly details?: unknown
  ) {
    super(message);
    this.name = "FileUploadError";
  }
}

export class FileProcessingError extends Error {
  public readonly type = ErrorType.FILE_PROCESSING;
  public readonly timestamp = new Date();

  constructor(
    message: string,
    public readonly fileName?: string,
    public readonly details?: unknown
  ) {
    super(message);
    this.name = "FileProcessingError";
  }
}

/**
 * Creates a standardized error object
 */
export const createError = (
  type: ErrorType,
  message: string,
  code?: string,
  details?: unknown
): AppError => ({
  type,
  message,
  code,
  details,
  timestamp: new Date(),
});

/**
 * Handles unknown errors and converts them to AppError
 */
export const handleUnknownError = (error: unknown): AppError => {
  if (error instanceof AuthError) {
    return createError(
      ErrorType.AUTHENTICATION,
      error.message,
      error.code,
      error.details
    );
  }

  if (error instanceof NetworkError) {
    return createError(
      ErrorType.NETWORK,
      error.message,
      error.code,
      error.details
    );
  }

  if (error instanceof ValidationError) {
    return createError(
      ErrorType.VALIDATION,
      error.message,
      error.field,
      error.details
    );
  }

  if (error instanceof FileUploadError) {
    return createError(
      ErrorType.FILE_UPLOAD,
      error.message,
      error.fileName,
      error.details
    );
  }

  if (error instanceof FileProcessingError) {
    return createError(
      ErrorType.FILE_PROCESSING,
      error.message,
      error.fileName,
      error.details
    );
  }

  if (error instanceof Error) {
    return createError(
      ErrorType.UNKNOWN,
      error.message,
      undefined,
      error.stack
    );
  }

  return createError(
    ErrorType.UNKNOWN,
    "An unknown error occurred",
    undefined,
    error
  );
};

/**
 * Logs errors with consistent formatting
 */
export const logError = (error: AppError | unknown, context?: string): void => {
  const appError =
    error instanceof Error ? handleUnknownError(error) : (error as AppError);

  const logData = {
    type: appError.type,
    message: appError.message,
    code: appError.code,
    context,
    timestamp: appError.timestamp,
    details: appError.details,
  };

  console.error("Application Error:", logData);

  // In production, you might want to send this to an error tracking service
  // Example: Sentry.captureException(appError, { extra: logData });
};

/**
 * Determines if an error should be shown to the user
 */
export const isUserFacingError = (error: AppError): boolean => {
  return [
    ErrorType.AUTHENTICATION,
    ErrorType.AUTHORIZATION,
    ErrorType.VALIDATION,
    ErrorType.FILE_UPLOAD,
    ErrorType.FILE_PROCESSING,
  ].includes(error.type);
};

/**
 * Gets a user-friendly error message
 */
export const getUserFriendlyMessage = (error: AppError): string => {
  switch (error.type) {
    case ErrorType.AUTHENTICATION:
      return "Authentication failed. Please try logging in again.";
    case ErrorType.AUTHORIZATION:
      return "You do not have permission to perform this action.";
    case ErrorType.NETWORK:
      return "Network error. Please check your connection and try again.";
    case ErrorType.VALIDATION:
      return error.message; // Validation messages are usually user-friendly
    case ErrorType.FILE_UPLOAD:
      return error.message; // File upload messages are usually user-friendly
    case ErrorType.FILE_PROCESSING:
      return "Failed to process the file. Please try again.";
    default:
      return "An unexpected error occurred. Please try again later.";
  }
};
