{"compilerOptions": {"target": "es2018", "lib": ["dom", "es2018", "esnext.asynciterable"], "declaration": true, "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}, "sourceMap": true, "allowJs": true, "noEmit": true, "module": "esnext"}, "include": ["src/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}