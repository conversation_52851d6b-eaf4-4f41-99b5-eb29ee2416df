# Permission Management API - Quản lý Quyền truy cập

## Tổng quan
API quản lý quyền truy cập cho phép cấp quyền, thu hồi quyền và xem quyền truy cập cho files và folders.

**Base URL:** `/api/v1/permissions`

---

## 1. <PERSON><PERSON><PERSON> danh sách loại quyền

### `GET /api/v1/permissions/available`

<PERSON><PERSON><PERSON> danh sách các loại quyền có thể cấp phát trong hệ thống.

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": [
    {
      "id": 1,
      "name": "Read",
      "displayName": "Xem",
      "description": "Quyền xem và tải xuống file/folder",
      "icon": "eye",
      "color": "blue"
    },
    {
      "id": 2,
      "name": "Write",
      "displayName": "Chỉnh sửa",
      "description": "Quyền tạo, sửa, xóa file trong folder",
      "icon": "edit",
      "color": "green"
    },
    {
      "id": 3,
      "name": "Delete",
      "displayName": "Xóa",
      "description": "Quyền xóa file/folder",
      "icon": "trash",
      "color": "red"
    },
    {
      "id": 4,
      "name": "Share",
      "displayName": "Chia sẻ",
      "description": "Quyền tạo link chia sẻ",
      "icon": "share",
      "color": "purple"
    },
    {
      "id": 5,
      "name": "ManagePermissions",
      "displayName": "Quản lý quyền",
      "description": "Quyền cấp/thu hồi quyền cho user khác",
      "icon": "key",
      "color": "orange"
    }
  ]
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Lấy danh sách thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `500 Internal Server Error`: Lỗi server

---

## 2. Cấp quyền cho file

### `POST /api/v1/permissions/files/{fileId}/grant`

Cấp quyền truy cập file cho user khác.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `fileId` | UUID | Có | ID của file |

#### Request Body

```json
{
  "userId": "target-user-id-123",
  "userEmail": "<EMAIL>",
  "permissionType": "Read",
  "expiresAt": "2023-06-30T23:59:59Z",
  "note": "Chia sẻ cho review báo cáo"
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `userId` | UUID | Có* | ID của user được cấp quyền |
| `userEmail` | string | Có* | Email của user (nếu không có userId) |
| `permissionType` | string | Có | Loại quyền: `Read`, `Write`, `Delete`, `Share`, `ManagePermissions` |
| `expiresAt` | datetime | Không | Thời gian hết hạn quyền |
| `note` | string | Không | Ghi chú về việc cấp quyền |

*Chỉ cần một trong `userId` hoặc `userEmail`

#### Response Body

**Thành công:**
```json
{
  "success": true,
  "message": "Permission granted successfully",
  "data": {
    "id": "perm123-456-789",
    "fileId": "file123-456-789",
    "fileName": "report-march.pdf",
    "userId": "target-user-id-123",
    "userEmail": "<EMAIL>",
    "permissionType": "Read",
    "grantedAt": "2023-03-22T15:00:00Z",
    "expiresAt": "2023-06-30T23:59:59Z",
    "isActive": true
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `201 Created`: Cấp quyền thành công

**❌ Lỗi:**
- `400 Bad Request`: Thông tin user không hợp lệ, loại quyền không hợp lệ
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền cấp phát quyền này
- `404 Not Found`: File hoặc user không tồn tại
- `409 Conflict`: User đã có quyền này rồi

#### Ví dụ sử dụng

```bash
# Cấp quyền xem cho user
POST /api/v1/permissions/files/file123/grant
{
  "userEmail": "<EMAIL>",
  "permissionType": "Read",
  "expiresAt": "2023-12-31T23:59:59Z"
}

# Cấp quyền chỉnh sửa vĩnh viễn
POST /api/v1/permissions/files/file123/grant
{
  "userId": "user456",
  "permissionType": "Write",
  "note": "Collaborator trên dự án ABC"
}
```

---

## 3. Cấp quyền cho folder

### `POST /api/v1/permissions/folders/{folderId}/grant`

Cấp quyền truy cập folder cho user khác.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `folderId` | UUID | Có | ID của folder |

#### Request Body

```json
{
  "userEmail": "<EMAIL>",
  "permissionType": "Write",
  "applyToSubfolders": true,
  "applyToFiles": true,
  "expiresAt": "2023-12-31T23:59:59Z",
  "note": "Quyền truy cập team cho Q4"
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `userId` | UUID | Có* | ID của user được cấp quyền |
| `userEmail` | string | Có* | Email của user |
| `permissionType` | string | Có | Loại quyền |
| `applyToSubfolders` | boolean | Không | Áp dụng cho thư mục con |
| `applyToFiles` | boolean | Không | Áp dụng cho files trong folder |
| `expiresAt` | datetime | Không | Thời gian hết hạn |
| `note` | string | Không | Ghi chú |

#### Response Body

```json
{
  "success": true,
  "message": "Folder permission granted successfully",
  "data": {
    "id": "fperm123-456-789",
    "folderId": "folder123-456-789",
    "folderName": "Project Documents",
    "userId": "target-user-id-123",
    "userEmail": "<EMAIL>",
    "permissionType": "Write",
    "permissionDisplayName": "Chỉnh sửa",
    "grantedBy": "current-user-id",
    "grantedAt": "2023-03-22T15:00:00Z",
    "expiresAt": "2023-12-31T23:59:59Z",
    "applyToSubfolders": true,
    "applyToFiles": true,
    "note": "Quyền truy cập team cho Q4",
    "isActive": true,
    "affectedSubfolders": 15,
    "affectedFiles": 87
  }
}
```

---

## 4. Lấy quyền truy cập file

### `GET /api/v1/permissions/files/{fileId}`

Xem danh sách quyền truy cập của file.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `fileId` | UUID | Có | ID của file |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `includeExpired` | boolean | Không | Bao gồm quyền đã hết hạn |
| `permissionType` | string | Không | Lọc theo loại quyền |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "fileInfo": {
      "id": "file123-456-789",
      "name": "report-march.pdf",
      "ownerId": "owner-user-id",
      "ownerEmail": "<EMAIL>"
    },
    "permissions": [
      {
        "id": "perm123-456-789",
        "userId": "user456",
        "userEmail": "<EMAIL>",
        "userName": "Nguyễn Văn A",
        "permissionType": "Read",
        "permissionDisplayName": "Xem",
        "grantedBy": "owner-user-id",
        "grantedByEmail": "<EMAIL>",
        "grantedAt": "2023-03-22T15:00:00Z",
        "expiresAt": "2023-06-30T23:59:59Z",
        "note": "Chia sẻ cho review báo cáo",
        "isActive": true,
        "daysUntilExpiry": 98
      },
      {
        "id": "perm456-789-012",
        "userId": "user789",
        "userEmail": "<EMAIL>",
        "userName": "Trần Thị B",
        "permissionType": "Write",
        "permissionDisplayName": "Chỉnh sửa",
        "grantedBy": "owner-user-id",
        "grantedByEmail": "<EMAIL>",
        "grantedAt": "2023-03-20T10:00:00Z",
        "expiresAt": null,
        "note": "Editor chính của file",
        "isActive": true,
        "daysUntilExpiry": null
      }
    ],
    "summary": {
      "totalPermissions": 2,
      "activePermissions": 2,
      "expiredPermissions": 0,
      "permissionsByType": {
        "Read": 1,
        "Write": 1,
        "Delete": 0,
        "Share": 0,
        "ManagePermissions": 0
      }
    }
  }
}
```

#### Ví dụ sử dụng

```bash
# Lấy tất cả quyền active
GET /api/v1/permissions/files/file123

# Bao gồm quyền hết hạn
GET /api/v1/permissions/files/file123?includeExpired=true

# Chỉ lấy quyền Read
GET /api/v1/permissions/files/file123?permissionType=Read
```

---

## 5. Lấy quyền truy cập folder

### `GET /api/v1/permissions/folders/{folderId}`

Xem danh sách quyền truy cập của folder.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `folderId` | UUID | Có | ID của folder |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `includeExpired` | boolean | Không | Bao gồm quyền đã hết hạn |
| `includeInherited` | boolean | Không | Bao gồm quyền kế thừa |
| `permissionType` | string | Không | Lọc theo loại quyền |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "folderInfo": {
      "id": "folder123-456-789",
      "name": "Project Documents",
      "path": "/Projects/ABC/Documents",
      "ownerId": "owner-user-id",
      "ownerEmail": "<EMAIL>"
    },
    "permissions": [
      {
        "id": "fperm123-456-789",
        "userId": "user456",
        "userEmail": "<EMAIL>",
        "userName": "Team ABC",
        "permissionType": "Write",
        "permissionDisplayName": "Chỉnh sửa",
        "grantedBy": "owner-user-id",
        "grantedByEmail": "<EMAIL>",
        "grantedAt": "2023-03-22T15:00:00Z",
        "expiresAt": "2023-12-31T23:59:59Z",
        "applyToSubfolders": true,
        "applyToFiles": true,
        "note": "Quyền truy cập team cho Q4",
        "isActive": true,
        "isInherited": false,
        "daysUntilExpiry": 283
      }
    ],
    "inheritedPermissions": [
      {
        "id": "inherited-perm-123",
        "userId": "user789",
        "userEmail": "<EMAIL>",
        "userName": "Quản lý dự án",
        "permissionType": "ManagePermissions",
        "permissionDisplayName": "Quản lý quyền",
        "inheritedFrom": {
          "folderId": "parent-folder-id",
          "folderName": "Projects",
          "folderPath": "/Projects"
        },
        "grantedAt": "2023-03-01T09:00:00Z",
        "isActive": true
      }
    ],
    "summary": {
      "totalPermissions": 1,
      "activePermissions": 1,
      "inheritedPermissions": 1,
      "expiredPermissions": 0
    }
  }
}
```

---

## 6. Thu hồi quyền

### `DELETE /api/v1/permissions/{permissionId}`

Thu hồi quyền truy cập đã cấp.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `permissionId` | UUID | Có | ID của permission cần thu hồi |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `reason` | string | Không | Lý do thu hồi quyền |

#### Response Body

```json
{
  "success": true,
  "message": "Permission revoked successfully",
  "data": {
    "id": "perm123-456-789",
    "resourceType": "file",
    "resourceId": "file123-456-789",
    "resourceName": "report-march.pdf",
    "userId": "user456",
    "userEmail": "<EMAIL>",
    "permissionType": "Read",
    "revokedAt": "2023-03-22T16:00:00Z",
    "revokedBy": "current-user-id",
    "reason": "Project completed"
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Thu hồi quyền thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền thu hồi permission này
- `404 Not Found`: Permission không tồn tại
- `410 Gone`: Permission đã bị thu hồi hoặc hết hạn

#### Ví dụ sử dụng

```bash
# Thu hồi quyền đơn giản
DELETE /api/v1/permissions/perm123-456-789

# Thu hồi với lý do
DELETE /api/v1/permissions/perm123-456-789?reason=Project%20completed
```

---

## 7. Thu hồi tất cả quyền của user

### `DELETE /api/v1/permissions/users/{userId}/revoke-all`

Thu hồi tất cả quyền của một user trên files/folders của người dùng hiện tại.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `userId` | UUID | Có | ID của user cần thu hồi quyền |

#### Request Body

```json
{
  "resourceTypes": ["file", "folder"],
  "permissionTypes": ["Read", "Write"],
  "reason": "User left the project"
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `resourceTypes` | string[] | Không | Loại resource: `file`, `folder` |
| `permissionTypes` | string[] | Không | Loại quyền cần thu hồi |
| `reason` | string | Không | Lý do thu hồi |

#### Response Body

```json
{
  "success": true,
  "message": "Bulk revoke completed",
  "data": {
    "userId": "user456",
    "userEmail": "<EMAIL>",
    "totalPermissions": 15,
    "revokedPermissions": 12,
    "failedRevocations": 3,
    "results": {
      "files": {
        "total": 8,
        "revoked": 6,
        "failed": 2
      },
      "folders": {
        "total": 7,
        "revoked": 6,
        "failed": 1
      }
    },
    "revokedAt": "2023-03-22T16:30:00Z",
    "reason": "User left the project"
  }
}
```

---

## 8. Kiểm tra quyền

### `GET /api/v1/permissions/check`

Kiểm tra quyền truy cập của user hiện tại đối với resource cụ thể.

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `resourceType` | string | Có | Loại resource: `file`, `folder` |
| `resourceId` | UUID | Có | ID của resource |
| `permissionType` | string | Có | Loại quyền cần kiểm tra |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "hasPermission": true,
    "source": "direct",
    "grantedAt": "2023-03-22T15:00:00Z"
  }
}
```

#### Các trường hợp source

- `owner`: User là chủ sở hữu
- `direct`: Quyền được cấp trực tiếp
- `inherited`: Quyền kế thừa từ folder cha
- `none`: Không có quyền

#### Ví dụ sử dụng

```bash
# Kiểm tra quyền ghi file
GET /api/v1/permissions/check?resourceType=file&resourceId=file123&permissionType=Write

# Kiểm tra quyền xóa folder
GET /api/v1/permissions/check?resourceType=folder&resourceId=folder456&permissionType=Delete
```

---

## 9. Lấy quyền của user hiện tại

### `GET /api/v1/permissions/my-permissions`

Lấy danh sách tất cả quyền mà user hiện tại được cấp.

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mặc định | Mô tả |
|---------|------|----------|----------|-------|
| `page` | integer | Không | 1 | Số trang |
| `pageSize` | integer | Không | 20 | Số items/trang |
| `resourceType` | string | Không | - | Lọc theo loại: `file`, `folder` |
| `permissionType` | string | Không | - | Lọc theo quyền |
| `grantedBy` | string | Không | - | Lọc theo email người cấp |
| `includeExpired` | boolean | Không | false | Bao gồm quyền hết hạn |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "permissions": [
      {
        "id": "perm123-456-789",
        "resourceType": "file",
        "resourceId": "file123-456-789",
        "resourceName": "report-march.pdf",
        "resourcePath": "/Projects/ABC/Reports/report-march.pdf",
        "permissionType": "Read",
        "permissionDisplayName": "Xem",
        "grantedBy": "owner-user-id",
        "grantedByEmail": "<EMAIL>",
        "grantedByName": "Quản lý dự án",
        "grantedAt": "2023-03-22T15:00:00Z",
        "expiresAt": "2023-06-30T23:59:59Z",
        "note": "Review báo cáo Q1",
        "isActive": true,
        "daysUntilExpiry": 98
      }
    ],
    "totalCount": 25,
    "page": 1,
    "pageSize": 20,
    "totalPages": 2,
    "summary": {
      "totalPermissions": 25,
      "activePermissions": 23,
      "expiredPermissions": 2,
      "filePermissions": 15,
      "folderPermissions": 10
    }
  }
}
```

---

## Workflow sử dụng

### 1. Cấp quyền cho tài nguyên
```
1. GET /permissions/available → Xem loại quyền
2. POST /permissions/files/{id}/grant → Cấp quyền file
3. POST /permissions/folders/{id}/grant → Cấp quyền folder
4. GET /permissions/check → Kiểm tra quyền
```

### 2. Quản lý quyền đã cấp
```
1. GET /permissions/files/{id} → Xem quyền file
2. GET /permissions/folders/{id} → Xem quyền folder
3. DELETE /permissions/{id} → Thu hồi quyền
4. DELETE /permissions/users/{id}/revoke-all → Thu hồi tất cả
```

### 3. Xem quyền của bản thân
```
1. GET /permissions/my-permissions → Xem quyền được cấp
2. GET /permissions/check → Kiểm tra quyền cụ thể
```

## Error Codes tổng hợp

| Code | HTTP Status | Mô tả |
|------|-------------|-------|
| `PERMISSION_NOT_FOUND` | 404 | Quyền không tồn tại |
| `USER_NOT_FOUND` | 404 | User không tồn tại |
| `RESOURCE_NOT_FOUND` | 404 | File/folder không tồn tại |
| `PERMISSION_DENIED` | 403 | Không có quyền thực hiện thao tác |
| `PERMISSION_EXISTS` | 409 | User đã có quyền này |
| `PERMISSION_EXPIRED` | 410 | Quyền đã hết hạn |
| `INVALID_PERMISSION_TYPE` | 400 | Loại quyền không hợp lệ |
| `CANNOT_REVOKE_OWNER` | 400 | Không thể thu hồi quyền của chủ sở hữu |
| `CIRCULAR_PERMISSION` | 400 | Tạo vòng lặp quyền |

## Permission Hierarchy

Thứ tự quyền từ thấp đến cao:
1. **Read** - Xem và tải xuống
2. **Write** - Chỉnh sửa, tạo mới
3. **Share** - Tạo link chia sẻ
4. **Delete** - Xóa tài nguyên
5. **ManagePermissions** - Quản lý quyền

> **Lưu ý:** Quyền cao hơn bao gồm tất cả quyền thấp hơn. Ví dụ: Write bao gồm Read.
