/**
 * User information interfaces for the application
 */

export interface UserProfile {
  id: string;
  email: string;
  name?: string;
  firstName?: string;
  lastName?: string;
  username?: string;
  avatar?: string;
  phone?: string;
  department?: string;
  position?: string;
  address?: string;
  bio?: string;
  joinDate?: string;
  lastLogin?: string;
  isActive: boolean;
  roles?: string[];
  permissions?: string[];
  preferences?: UserPreferences;
}

export interface UserPreferences {
  language?: string;
  theme?: 'light' | 'dark' | 'auto';
  timezone?: string;
  notifications?: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
}

export interface AuthMeResponse {
  success: boolean;
  data: UserProfile;
  message?: string;
}

export interface UpdateUserProfileRequest {
  name?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  department?: string;
  position?: string;
  address?: string;
  bio?: string;
  avatar?: string;
  preferences?: Partial<UserPreferences>;
}

export interface UpdateUserProfileResponse {
  success: boolean;
  data: UserProfile;
  message?: string;
}
