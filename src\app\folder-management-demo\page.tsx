"use client";

import React, { useState } from "react";
import { EnhancedFolderManager } from "@/components/folder-manager/EnhancedFolderManager";
import { ApiClient } from "@/api/core/apiClient";
import { FolderDto, FileDto } from "@/api/types/interfaces";

// Mock API client for demonstration
const mockApiClient = new ApiClient({
  baseURL: "/api/v1",
  timeout: 30000,
});

export default function FolderManagementDemo() {
  const [selectedFile, setSelectedFile] = useState<FileDto | null>(null);
  const [selectedFolder, setSelectedFolder] = useState<FolderDto | null>(null);

  const handleFileSelect = (file: FileDto) => {
    setSelectedFile(file);
    console.log("Selected file:", file);
  };

  const handleFolderSelect = (folder: FolderDto) => {
    setSelectedFolder(folder);
    console.log("Selected folder:", folder);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Folder Management System Demo
              </h1>
              <p className="mt-2 text-sm text-gray-600">
                Comprehensive folder and file management with advanced filtering,
                sorting, and CRUD operations
              </p>
            </div>
            <div className="text-sm text-gray-500">
              <p>API Integration: Ready</p>
              <p>Real-time Updates: Enabled</p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar - Feature Overview */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 sticky top-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                System Features
              </h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    📁 Folder Operations
                  </h3>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>✓ Create & organize folders</li>
                    <li>✓ Move & copy operations</li>
                    <li>✓ Nested folder navigation</li>
                    <li>✓ Bulk operations</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    🔍 Search & Filter
                  </h3>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>✓ Real-time search</li>
                    <li>✓ Advanced filtering</li>
                    <li>✓ Sort by multiple criteria</li>
                    <li>✓ Date range filtering</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    🚀 API Integration
                  </h3>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>✓ RESTful API client</li>
                    <li>✓ Error handling</li>
                    <li>✓ Optimistic updates</li>
                    <li>✓ Pagination support</li>
                  </ul>
                </div>

                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">
                    🎨 UI/UX Features
                  </h3>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>✓ Responsive design</li>
                    <li>✓ Loading states</li>
                    <li>✓ Breadcrumb navigation</li>
                    <li>✓ Keyboard shortcuts</li>
                  </ul>
                </div>
              </div>

              {/* API Endpoints Mapped */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  API Endpoints Mapped
                </h3>
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span className="text-green-600">GET</span>
                    <span className="text-gray-600">/folders</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-600">POST</span>
                    <span className="text-gray-600">/folders</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-yellow-600">PUT</span>
                    <span className="text-gray-600">/folders/{id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-red-600">DELETE</span>
                    <span className="text-gray-600">/folders/{id}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-green-600">GET</span>
                    <span className="text-gray-600">/folders/{id}/contents</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-purple-600">POST</span>
                    <span className="text-gray-600">/folders/bulk</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Folder Manager */}
          <div className="lg:col-span-3">
            <EnhancedFolderManager
              apiClient={mockApiClient}
              onFileSelect={handleFileSelect}
              onFolderSelect={handleFolderSelect}
            />

            {/* Selection Info */}
            {(selectedFile || selectedFolder) && (
              <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                  Selection Details
                </h2>

                {selectedFile && (
                  <div className="mb-4 p-4 bg-blue-50 rounded-lg">
                    <h3 className="text-sm font-medium text-blue-900 mb-2">
                      📄 Selected File
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-blue-800">Name:</span>
                        <span className="ml-2 text-blue-700">
                          {selectedFile.displayName || selectedFile.name}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">Size:</span>
                        <span className="ml-2 text-blue-700">
                          {(selectedFile.fileSize / 1024 / 1024).toFixed(2)} MB
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">Type:</span>
                        <span className="ml-2 text-blue-700">
                          {selectedFile.mimeType}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-blue-800">Modified:</span>
                        <span className="ml-2 text-blue-700">
                          {new Date(selectedFile.updatedAt).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                {selectedFolder && (
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h3 className="text-sm font-medium text-green-900 mb-2">
                      📁 Selected Folder
                    </h3>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium text-green-800">Name:</span>
                        <span className="ml-2 text-green-700">
                          {selectedFolder.name}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-green-800">Path:</span>
                        <span className="ml-2 text-green-700">
                          {selectedFolder.path}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-green-800">Items:</span>
                        <span className="ml-2 text-green-700">
                          {selectedFolder.fileCount + selectedFolder.subfolderCount}
                        </span>
                      </div>
                      <div>
                        <span className="font-medium text-green-800">Created:</span>
                        <span className="ml-2 text-green-700">
                          {new Date(selectedFolder.createdAt).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    {selectedFolder.description && (
                      <div className="mt-3">
                        <span className="font-medium text-green-800">Description:</span>
                        <p className="mt-1 text-green-700 text-sm">
                          {selectedFolder.description}
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Footer with Implementation Notes */}
        <div className="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Implementation Notes
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                📋 Component Architecture
              </h3>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• EnhancedFolderManager: Main component</li>
                <li>• EnhancedFolderModal: CRUD operations</li>
                <li>• FolderService: API integration</li>
                <li>• TypeScript interfaces for type safety</li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                🔧 State Management
              </h3>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• React useState for local state</li>
                <li>• useCallback for performance</li>
                <li>• Optimistic UI updates</li>
                <li>• Error boundary handling</li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-700 mb-2">
                🎯 Key Features Mapping
              </h3>
              <ul className="text-xs text-gray-600 space-y-1">
                <li>• Search → API query parameters</li>
                <li>• Pagination → Server-side pagination</li>
                <li>• Sorting → API sort fields</li>
                <li>• Filtering → Advanced query options</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h3 className="text-sm font-medium text-yellow-800 mb-2">
              ⚠️ Demo Limitations
            </h3>
            <p className="text-xs text-yellow-700">
              This demo uses mock data and API client. In a production environment,
              you would connect this to your actual backend API endpoints. The UI and
              interaction patterns are fully functional and ready for integration.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
