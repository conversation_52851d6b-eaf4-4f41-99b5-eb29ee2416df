import { ApiClient } from '../core/apiClient';
import {
  FolderDto,
  FolderCreateData,
  FolderUpdateData,
  FolderContentsOptions,
  FolderContentsResponse,
  FolderDownloadOptions,
  SortField,
  SortDirection,
  PaginationInfo,
  Permission,
  ErrorCode
} from '../types/interfaces';
import { ValidationApiError, ApiError } from '../core/apiClient';

export class FolderService {
  private client: ApiClient;

  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }

  /**
   * Create a new folder
   * @param folderData Folder creation data
   * @returns Information about the created folder
   */
  async create(folderData: FolderCreateData): Promise<FolderDto> {
    this.validateCreateData(folderData);

    try {
      return await this.client.post<FolderDto>('/folders', folderData);
    } catch (error) {
      throw this.handleCreateError(error as ApiError);
    }
  }

  /**
   * Get folder by ID
   * @param folderId ID of the folder to retrieve
   * @returns Folder information
   */
  async getById(folderId: string): Promise<FolderDto> {
    this.validateId(folderId, 'folderId');

    try {
      return await this.client.get<FolderDto>(`/folders/${folderId}`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Folder');
    }
  }

  /**
   * List folders with pagination and filtering
   * @param options Query options for listing folders
   * @returns Paginated list of folders
   */
  async list(options?: {
    page?: number;
    pageSize?: number;
    sortBy?: SortField;
    sortDirection?: SortDirection;
    parentFolderId?: string;
    path?: string;
  }): Promise<{ folders: FolderDto[]; pagination: PaginationInfo }> {
    const params: Record<string, any> = {};

    if (options?.page) params.page = options.page;
    if (options?.pageSize) params.pageSize = options.pageSize;
    if (options?.sortBy) params.sortBy = options.sortBy;
    if (options?.sortDirection) params.sortDirection = options.sortDirection;
    if (options?.parentFolderId) params.parentFolderId = options.parentFolderId;
    if (options?.path) params.path = options.path;

    return await this.client.get<{ folders: FolderDto[]; pagination: PaginationInfo }>('/folders', { params });
  }

  /**
   * Get the contents of a folder (files and subfolders)
   * @param folderId ID of the folder
   * @param options Filtering and pagination options
   * @returns Folder contents with pagination
   */
  async getContents(folderId: string, options?: FolderContentsOptions): Promise<FolderContentsResponse> {
    this.validateId(folderId, 'folderId');
    this.validateContentsOptions(options);

    try {
      return await this.client.get<FolderContentsResponse>(`/folders/${folderId}/contents`, {
        params: options
      });
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Folder');
    }
  }

  /**
   * Get folder tree structure
   * @param folderId Root folder ID (optional, defaults to user's root)
   * @param depth Maximum depth to retrieve (optional, defaults to all)
   * @returns Hierarchical folder structure
   */
  async getTree(folderId?: string, depth?: number): Promise<FolderDto[]> {
    const params: Record<string, any> = {};
    if (folderId) params.folderId = folderId;
    if (depth) params.depth = depth;

    try {
      return await this.client.get<FolderDto[]>('/folders/tree', { params });
    } catch (error) {
      throw this.handleTreeError(error as ApiError);
    }
  }

  /**
   * Get folder path (breadcrumb)
   * @param folderId ID of the folder
   * @returns Array of folders representing the path from root to target folder
   */
  async getPath(folderId: string): Promise<FolderDto[]> {
    this.validateId(folderId, 'folderId');

    try {
      return await this.client.get<FolderDto[]>(`/folders/${folderId}/path`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Folder');
    }
  }

  /**
   * Download folder as ZIP
   * @param folderId ID of the folder to download
   * @param options Download options
   * @returns Blob containing the ZIP file
   */
  async download(folderId: string, options?: FolderDownloadOptions): Promise<Blob> {
    this.validateId(folderId, 'folderId');
    this.validateDownloadOptions(options);

    const params: Record<string, any> = {};
    if (options?.includeSubfolders !== undefined) params.includeSubfolders = options.includeSubfolders;
    if (options?.maxZipSize) params.maxZipSize = options.maxZipSize;

    try {
      return await this.client.downloadFile(`/folders/${folderId}/download`, { params });
    } catch (error) {
      throw this.handleDownloadError(error as ApiError);
    }
  }

  /**
   * Update folder metadata
   * @param folderId ID of the folder
   * @param data Updated folder data
   * @returns Updated folder information
   */
  async update(folderId: string, data: FolderUpdateData): Promise<FolderDto> {
    this.validateId(folderId, 'folderId');
    this.validateUpdateData(data);

    try {
      return await this.client.put<FolderDto>(`/folders/${folderId}`, data);
    } catch (error) {
      throw this.handleUpdateError(error as ApiError);
    }
  }

  /**
   * Delete a folder
   * @param folderId ID of the folder to delete
   * @param permanent Whether to permanently delete the folder
   * @param recursive Whether to delete non-empty folders (default: false)
   */
  async delete(folderId: string, permanent: boolean = false, recursive: boolean = false): Promise<void> {
    this.validateId(folderId, 'folderId');

    try {
      await this.client.delete(`/folders/${folderId}`, {
        params: { permanent, recursive }
      });
    } catch (error) {
      throw this.handleDeleteError(error as ApiError);
    }
  }

  /**
   * Move a folder to another parent folder
   * @param folderId ID of the folder to move
   * @param targetFolderId ID of the destination parent folder
   */
  async move(folderId: string, targetFolderId: string): Promise<void> {
    this.validateId(folderId, 'folderId');
    this.validateId(targetFolderId, 'targetFolderId');

    if (folderId === targetFolderId) {
      throw new ValidationApiError('Cannot move folder to itself', [
        { field: 'targetFolderId', message: 'Target folder cannot be the same as source folder', code: 'INVALID_OPERATION' }
      ]);
    }

    try {
      await this.client.post(`/folders/${folderId}/move`, { targetFolderId });
    } catch (error) {
      throw this.handleMoveError(error as ApiError);
    }
  }

  /**
   * Copy a folder to another location
   * @param folderId ID of the folder to copy
   * @param targetFolderId ID of the destination parent folder
   * @param newName Optional new name for the copied folder
   * @param includeSubfolders Whether to copy subfolders (default: true)
   * @returns Information about the copied folder
   */
  async copy(
    folderId: string,
    targetFolderId: string,
    newName?: string,
    includeSubfolders: boolean = true
  ): Promise<FolderDto> {
    this.validateId(folderId, 'folderId');
    this.validateId(targetFolderId, 'targetFolderId');

    if (folderId === targetFolderId) {
      throw new ValidationApiError('Cannot copy folder to itself', [
        { field: 'targetFolderId', message: 'Target folder cannot be the same as source folder', code: 'INVALID_OPERATION' }
      ]);
    }

    const payload = {
      targetFolderId,
      ...(newName && { newName }),
      includeSubfolders
    };

    try {
      return await this.client.post<FolderDto>(`/folders/${folderId}/copy`, payload);
    } catch (error) {
      throw this.handleCopyError(error as ApiError);
    }
  }

  /**
   * Get folder statistics
   * @param folderId ID of the folder
   * @returns Statistics about the folder (size, file count, etc.)
   */
  async getStatistics(folderId: string): Promise<{
    totalFiles: number;
    totalSubfolders: number;
    totalSize: number;
    lastModified: string;
    fileTypes: Record<string, number>;
  }> {
    this.validateId(folderId, 'folderId');

    try {
      return await this.client.get(`/folders/${folderId}/statistics`);
    } catch (error) {
      throw this.handleNotFoundError(error as ApiError, 'Folder');
    }
  }

  /**
   * Search within a folder
   * @param folderId ID of the folder to search in
   * @param query Search query
   * @param options Search options
   * @returns Search results
   */
  async search(
    folderId: string,
    query: string,
    options?: {
      recursive?: boolean;
      fileTypes?: string[];
      maxResults?: number;
    }
  ): Promise<{
    files: any[];
    folders: FolderDto[];
    totalResults: number;
  }> {
    this.validateId(folderId, 'folderId');

    if (!query || query.trim().length === 0) {
      throw new ValidationApiError('Search query is required', [
        { field: 'query', message: 'Search query cannot be empty', code: 'REQUIRED' }
      ]);
    }

    const params: Record<string, any> = { query };
    if (options?.recursive !== undefined) params.recursive = options.recursive;
    if (options?.fileTypes) params.fileTypes = options.fileTypes.join(',');
    if (options?.maxResults) params.maxResults = options.maxResults;

    try {
      return await this.client.get(`/folders/${folderId}/search`, { params });
    } catch (error) {
      throw this.handleSearchError(error as ApiError);
    }
  }

  // Validation methods

  private validateId(id: string, fieldName: string): void {
    if (!id || typeof id !== 'string' || id.trim().length === 0) {
      throw new ValidationApiError(`Invalid ${fieldName}`, [
        { field: fieldName, message: `${fieldName} is required and must be a non-empty string`, code: 'REQUIRED' }
      ]);
    }
  }

  private validateCreateData(data: FolderCreateData): void {
    if (!data) {
      throw new ValidationApiError('Folder data is required', [
        { field: 'data', message: 'Folder creation data is required', code: 'REQUIRED' }
      ]);
    }

    if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
      throw new ValidationApiError('Folder name is required', [
        { field: 'name', message: 'Folder name is required and must be a non-empty string', code: 'REQUIRED' }
      ]);
    }

    // Validate folder name format
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(data.name)) {
      throw new ValidationApiError('Invalid folder name', [
        { field: 'name', message: 'Folder name contains invalid characters', code: 'INVALID_FORMAT' }
      ]);
    }

    if (data.name.length > 255) {
      throw new ValidationApiError('Folder name too long', [
        { field: 'name', message: 'Folder name cannot exceed 255 characters', code: 'TOO_LONG' }
      ]);
    }
  }

  private validateUpdateData(data: FolderUpdateData): void {
    if (!data || Object.keys(data).length === 0) {
      throw new ValidationApiError('Update data is required', [
        { field: 'data', message: 'At least one field must be provided for update', code: 'REQUIRED' }
      ]);
    }

    if (data.name !== undefined) {
      if (!data.name || typeof data.name !== 'string' || data.name.trim().length === 0) {
        throw new ValidationApiError('Invalid folder name', [
          { field: 'name', message: 'Folder name must be a non-empty string', code: 'INVALID_VALUE' }
        ]);
      }

      const invalidChars = /[<>:"/\\|?*]/;
      if (invalidChars.test(data.name)) {
        throw new ValidationApiError('Invalid folder name', [
          { field: 'name', message: 'Folder name contains invalid characters', code: 'INVALID_FORMAT' }
        ]);
      }
    }
  }

  private validateContentsOptions(options?: FolderContentsOptions): void {
    if (!options) return;

    if (options.page && (options.page < 1 || !Number.isInteger(options.page))) {
      throw new ValidationApiError('Invalid page number', [
        { field: 'page', message: 'Page number must be a positive integer', code: 'INVALID_VALUE' }
      ]);
    }

    if (options.pageSize && (options.pageSize < 1 || options.pageSize > 100 || !Number.isInteger(options.pageSize))) {
      throw new ValidationApiError('Invalid page size', [
        { field: 'pageSize', message: 'Page size must be between 1 and 100', code: 'INVALID_VALUE' }
      ]);
    }
  }

  private validateDownloadOptions(options?: FolderDownloadOptions): void {
    if (!options) return;

    if (options.maxZipSize && (options.maxZipSize < 1 || options.maxZipSize > 5 * 1024 * 1024 * 1024)) {
      throw new ValidationApiError('Invalid max ZIP size', [
        { field: 'maxZipSize', message: 'Max ZIP size must be between 1 byte and 5GB', code: 'INVALID_VALUE' }
      ]);
    }
  }

  // Error handling methods

  private handleCreateError(error: ApiError): ApiError {
    if (error.statusCode === 409) {
      return new ApiError('Folder already exists', 409, ErrorCode.DUPLICATE_FILE_NAME, error.correlationId);
    }
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to create folder', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 404) {
      return new ApiError('Parent folder not found', 404, ErrorCode.INVALID_PARENT_FOLDER, error.correlationId);
    }
    return error;
  }

  private handleUpdateError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to update folder', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 409) {
      return new ApiError('Folder name already exists', 409, ErrorCode.DUPLICATE_FILE_NAME, error.correlationId);
    }
    return this.handleNotFoundError(error, 'Folder');
  }

  private handleDeleteError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to delete folder', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 409) {
      return new ApiError('Folder is not empty', 409, 'FOLDER_NOT_EMPTY', error.correlationId);
    }
    return this.handleNotFoundError(error, 'Folder');
  }

  private handleMoveError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to move folder', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 409) {
      return new ApiError('Cannot move folder: would create circular reference', 409, 'CIRCULAR_REFERENCE', error.correlationId);
    }
    return error;
  }

  private handleCopyError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to copy folder', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 507) {
      return new ApiError('Insufficient storage space', 507, ErrorCode.STORAGE_QUOTA_EXCEEDED, error.correlationId);
    }
    return error;
  }

  private handleDownloadError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to download folder', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    if (error.statusCode === 413) {
      return new ApiError('Folder too large to download', 413, 'FOLDER_TOO_LARGE', error.correlationId);
    }
    return this.handleNotFoundError(error, 'Folder');
  }

  private handleTreeError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to access folder tree', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return error;
  }

  private handleSearchError(error: ApiError): ApiError {
    if (error.statusCode === 403) {
      return new ApiError('Insufficient permissions to search folder', 403, ErrorCode.INSUFFICIENT_PERMISSIONS, error.correlationId);
    }
    return this.handleNotFoundError(error, 'Folder');
  }

  private handleNotFoundError(error: ApiError, resourceType: string): ApiError {
    if (error.statusCode === 404) {
      return new ApiError(`${resourceType} not found`, 404, ErrorCode.RESOURCE_NOT_FOUND, error.correlationId);
    }
    return error;
  }
}
