import { useCallback, useState } from "react";
import { User } from "oidc-client-ts";

/**
 * Custom hook for managing authentication state
 * Separates state management logic for better testability
 */
export const useAuthState = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const setAuthenticatedUser = useCallback((authUser: User) => {
    setUser(authUser);
    setIsAuthenticated(true);
    setIsLoading(false);
    setError(null);
  }, []);

  const clearUser = useCallback(() => {
    setUser(null);
    setIsAuthenticated(false);
    setIsLoading(false);
  }, []);

  const setAuthError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    setIsLoading(false);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const setLoadingState = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  // Utility functions
  const hasRole = useCallback(
    (role: string): boolean => {
      return user?.profile?.roles
        ? (user.profile.roles as string[]).includes(role)
        : false;
    },
    [user]
  );

  const hasPermission = useCallback(
    (permission: string): boolean => {
      return user?.profile?.permissions
        ? (user.profile.permissions as string[]).includes(permission)
        : false;
    },
    [user]
  );

  const hasAnyRole = useCallback(
    (roles: string[]): boolean => {
      return roles.some((role) => hasRole(role));
    },
    [hasRole]
  );

  const hasAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      return permissions.some((permission) => hasPermission(permission));
    },
    [hasPermission]
  );

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    error,

    // Actions
    setAuthenticatedUser,
    clearUser,
    setAuthError,
    clearError,
    setLoadingState,

    // Utilities
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAnyPermission,
  };
};
