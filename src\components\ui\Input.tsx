import React from "react";
import { cn } from "@/lib/utils";

/**
 * Input component variants and sizes
 */
export type InputVariant = "default" | "error" | "success";
export type InputSize = "sm" | "md" | "lg";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: InputVariant;
  inputSize?: InputSize;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  label?: string;
  helperText?: string;
  errorMessage?: string;
  isRequired?: boolean;
}

const inputVariants: Record<InputVariant, string> = {
  default: "border-gray-300 focus:border-primary-500 focus:ring-primary-500",
  error: "border-red-300 focus:border-red-500 focus:ring-red-500",
  success: "border-green-300 focus:border-green-500 focus:ring-green-500",
};

const inputSizes: Record<InputSize, string> = {
  sm: "px-3 py-1.5 text-sm",
  md: "px-4 py-2 text-base",
  lg: "px-4 py-3 text-lg",
};

/**
 * Reusable Input component with consistent styling
 */
export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant = "default",
      inputSize = "md",
      leftIcon,
      rightIcon,
      label,
      helperText,
      errorMessage,
      isRequired = false,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = variant === "error" || !!errorMessage;
    const actualVariant = hasError ? "error" : variant;

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className={cn(
              "block text-sm font-medium mb-1",
              hasError ? "text-red-700" : "text-gray-700"
            )}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <span className="text-gray-400">{leftIcon}</span>
            </div>
          )}

          <input
            ref={ref}
            id={inputId}
            className={cn(
              // Base styles
              "block w-full border rounded-md shadow-sm",
              "focus:outline-none focus:ring-2 focus:ring-offset-0",
              "disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed",
              "placeholder:text-gray-400",

              // Variant styles
              inputVariants[actualVariant],

              // Size styles
              inputSizes[inputSize],

              // Icon padding
              leftIcon && "pl-10",
              rightIcon && "pr-10",

              // Custom className
              className
            )}
            {...props}
          />

          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-gray-400">{rightIcon}</span>
            </div>
          )}
        </div>

        {(helperText || errorMessage) && (
          <p
            className={cn(
              "mt-1 text-sm",
              hasError ? "text-red-600" : "text-gray-500"
            )}
          >
            {errorMessage || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = "Input";

/**
 * Textarea component with consistent styling
 */
export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  variant?: InputVariant;
  label?: string;
  helperText?: string;
  errorMessage?: string;
  isRequired?: boolean;
  resize?: "none" | "vertical" | "horizontal" | "both";
}

export const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      variant = "default",
      label,
      helperText,
      errorMessage,
      isRequired = false,
      resize = "vertical",
      id,
      ...props
    },
    ref
  ) => {
    const textareaId =
      id || `textarea-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = variant === "error" || !!errorMessage;
    const actualVariant = hasError ? "error" : variant;

    const resizeClasses = {
      none: "resize-none",
      vertical: "resize-y",
      horizontal: "resize-x",
      both: "resize",
    };

    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={textareaId}
            className={cn(
              "block text-sm font-medium mb-1",
              hasError ? "text-red-700" : "text-gray-700"
            )}
          >
            {label}
            {isRequired && <span className="text-red-500 ml-1">*</span>}
          </label>
        )}

        <textarea
          ref={ref}
          id={textareaId}
          className={cn(
            // Base styles
            "block w-full border rounded-md shadow-sm",
            "focus:outline-none focus:ring-2 focus:ring-offset-0",
            "disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed",
            "placeholder:text-gray-400",
            "px-4 py-2 text-base",

            // Variant styles
            inputVariants[actualVariant],

            // Resize styles
            resizeClasses[resize],

            // Custom className
            className
          )}
          {...props}
        />

        {(helperText || errorMessage) && (
          <p
            className={cn(
              "mt-1 text-sm",
              hasError ? "text-red-600" : "text-gray-500"
            )}
          >
            {errorMessage || helperText}
          </p>
        )}
      </div>
    );
  }
);

Textarea.displayName = "Textarea";

/**
 * Search Input component with built-in search icon
 */
export interface SearchInputProps
  extends Omit<InputProps, "leftIcon" | "type"> {
  onSearch?: (value: string) => void;
  onClear?: () => void;
  showClearButton?: boolean;
}

export const SearchInput = React.forwardRef<HTMLInputElement, SearchInputProps>(
  (
    { onSearch, onClear, showClearButton = true, value, onChange, ...props },
    ref
  ) => {
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (e.key === "Enter" && onSearch) {
        onSearch(e.currentTarget.value);
      }
    };

    const handleClear = () => {
      if (onClear) {
        onClear();
      }
    };

    const SearchIcon = () => (
      <svg
        className="h-5 w-5"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
        />
      </svg>
    );

    const ClearIcon = () => (
      <button
        type="button"
        onClick={handleClear}
        className="text-gray-400 hover:text-gray-600 focus:outline-none"
      >
        <svg
          className="h-5 w-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    );

    return (
      <Input
        ref={ref}
        type="search"
        leftIcon={<SearchIcon />}
        rightIcon={showClearButton && value ? <ClearIcon /> : undefined}
        value={value}
        onChange={onChange}
        onKeyDown={handleKeyDown}
        {...props}
      />
    );
  }
);

SearchInput.displayName = "SearchInput";
