"use client";

import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  Bars3Icon,
  BellIcon,
  ArrowRightStartOnRectangleIcon,
} from "@heroicons/react/24/outline";

interface HeaderProps {
  onMenuClick?: () => void;
}

/**
 * Application Header Component
 * Provides navigation, search, notifications, and user profile
 */
export function Header({ onMenuClick }: HeaderProps) {
  const { user, logout, isLoading } = useAuth();

  const handleLogout = async () => {
    try {
      await logout();
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  return (
    <div
      className="sticky top-0 z-40 flex h-16 shrink-0 items-center gap-x-4 px-4 sm:gap-x-6 sm:px-6 lg:px-8 animate-fade-in"
      style={{
        background: "var(--surface-elevated)",
        borderBottom: "1px solid var(--border-light)",
        boxShadow: "var(--shadow-sm)",
      }}
    >
      {/* Mobile menu button */}
      <button
        type="button"
        className="-m-2.5 p-2.5 lg:hidden btn-secondary"
        style={{
          background: "transparent",
          border: "none",
          color: "var(--text-secondary)",
          borderRadius: "var(--radius-md)",
          transition: "all 0.2s ease",
        }}
        onClick={onMenuClick}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = "var(--surface)";
          e.currentTarget.style.color = "var(--text-primary)";
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = "transparent";
          e.currentTarget.style.color = "var(--text-secondary)";
        }}
      >
        <span className="sr-only">Open sidebar</span>
        <Bars3Icon className="h-6 w-6" aria-hidden="true" />
      </button>

      {/* Separator */}
      <div
        className="h-6 w-px lg:hidden"
        style={{ background: "var(--border)" }}
        aria-hidden="true"
      />

      <div className="flex flex-1 gap-x-4 self-stretch lg:gap-x-6">
        {/* Search */}
        <div className="relative flex flex-1">
          {/* <form action="#" method="GET">
            <label htmlFor="search-field" className="sr-only">
              Search
            </label>
            <input
              id="search-field"
              className="block h-full w-full border-0 py-0 pl-8 pr-0 text-gray-900 placeholder:text-gray-400 focus:ring-0 sm:text-sm"
              placeholder="Search files..."
              type="search"
              name="search"
            />
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <svg
                className="h-5 w-5 text-gray-400"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </form> */}
        </div>
        <div className="flex items-center gap-x-4 lg:gap-x-6">
          {/* Notifications */}
          <button
            type="button"
            className="-m-2.5 p-2.5 rounded-lg transition-all duration-200"
            style={{
              color: "var(--text-tertiary)",
              background: "transparent",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = "var(--surface)";
              e.currentTarget.style.color = "var(--text-secondary)";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = "transparent";
              e.currentTarget.style.color = "var(--text-tertiary)";
            }}
          >
            <span className="sr-only">View notifications</span>
            <BellIcon className="h-6 w-6" aria-hidden="true" />
          </button>

          {/* Separator */}
          <div
            className="hidden lg:block lg:h-6 lg:w-px"
            style={{ background: "var(--border)" }}
            aria-hidden="true"
          />

          {/* Profile section */}
          <div className="relative">
            <div className="flex items-center gap-x-3">
              <div
                className="h-8 w-8 rounded-full flex items-center justify-center"
                style={{
                  background:
                    "linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%)",
                  color: "var(--text-inverse)",
                }}
              >
                <span className="text-sm font-semibold">
                  {user?.name?.charAt(0)?.toUpperCase() || "U"}
                </span>
              </div>
              <div className="hidden lg:block">
                <p
                  className="text-sm font-semibold leading-6"
                  style={{ color: "var(--text-primary)" }}
                >
                  {user?.name || "Demo User"}
                </p>
                <p
                  className="text-xs leading-5"
                  style={{ color: "var(--text-tertiary)" }}
                >
                  {user?.email || "<EMAIL>"}
                </p>
              </div>
              <button
                type="button"
                onClick={handleLogout}
                disabled={isLoading}
                className="-m-2.5 p-2.5 rounded-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  color: "var(--text-tertiary)",
                  background: "transparent",
                }}
                onMouseEnter={(e) => {
                  if (!isLoading) {
                    e.currentTarget.style.background = "var(--surface)";
                    e.currentTarget.style.color = "var(--accent-red)";
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.background = "transparent";
                  e.currentTarget.style.color = "var(--text-tertiary)";
                }}
                title="Logout"
              >
                <span className="sr-only">Logout</span>
                <ArrowRightStartOnRectangleIcon
                  className="h-6 w-6"
                  aria-hidden="true"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
