# VeasyFileManager API Client

A TypeScript client library for interacting with the VeasyFileManager API.

## Features

- Complete TypeScript support with full type definitions
- Comprehensive file management (upload, download, copy, move)
- Folder operations (create, list, download as zip)
- Permission management
- File sharing with security options
- Advanced chunked upload for large files with:
  - Automatic retry with exponential backoff
  - <PERSON><PERSON><PERSON> uploads with concurrency control
  - Progress tracking
  - Upload pause/resume support
- React hooks for easy integration

## Installation

```bash
npm install veasy-file-manager
```

## Basic Usage

```typescript
import VeasyFileManagerAPI from "veasy-file-manager";

// Create an API client instance
const api = new VeasyFileManagerAPI("/api/v1", "your-jwt-token");

// Simple file upload
const file = document.getElementById("fileInput").files[0];
const result = await api.files.upload(file, {
  parentFolderId: "folder-uuid",
  displayName: "Custom Name.pdf",
  description: "File description",
  syncToGoogleDrive: true,
});

// Download a file
const blob = await api.files.download("file-uuid");
const url = URL.createObjectURL(blob);
window.open(url);

// Get folder contents
const folderContents = await api.folders.getContents("folder-uuid", {
  page: 1,
  pageSize: 20,
  sortBy: "Name",
  sortDirection: "ASC",
});
```

## Chunked Upload for Large Files

```typescript
// Upload a large file with progress tracking
const file = document.getElementById("largeFileInput").files[0];
await api.chunkedUpload.uploadFile(file, {
  parentFolderId: "folder-uuid",
  onProgress: (progress) => {
    console.log(`Upload progress: ${progress.progress}%`);
    console.log(`Chunks: ${progress.uploadedChunks}/${progress.totalChunks}`);
  },
});

// Resume an interrupted upload
await api.chunkedUpload.resumeUpload("session-id", file, {
  onProgress: (progress) => {
    console.log(`Resume progress: ${progress.progress}%`);
  },
});
```

## File Sharing

```typescript
// Create a password-protected share link
const shareLink = await api.files.createShareLink("file-uuid", {
  shareType: "Password",
  password: "secure123",
  expiresAt: "2023-12-31T23:59:59Z",
  maxDownloads: 10,
});

// Access a shared file (this endpoint is public)
const sharedFile = await api.files.accessSharedFile("share-token", "password");
```

## React Integration

```tsx
import React from "react";
import { VeasyFileManagerAPI, FileUploader } from "veasy-file-manager";

const FileUploadPage: React.FC = () => {
  const api = new VeasyFileManagerAPI("/api/v1", "jwt-token");

  const handleUploadComplete = (files) => {
    console.log("Files uploaded:", files);
  };

  return (
    <div>
      <h1>File Upload</h1>

      <FileUploader
        api={api}
        parentFolderId="folder-uuid"
        acceptedFileTypes=".pdf,.docx,image/*"
        maxFileSize={50 * 1024 * 1024} // 50MB
        multiple={true}
        onUploadComplete={handleUploadComplete}
        autoUpload={true}
      />
    </div>
  );
};
```

Or use the hook directly for more control:

```tsx
import React, { useState } from "react";
import { VeasyFileManagerAPI, useFileUpload } from "veasy-file-manager";

const CustomUploader: React.FC = () => {
  const api = new VeasyFileManagerAPI("/api/v1", "jwt-token");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const { upload, chunkedUpload, progress, isUploading, cancelUpload } =
    useFileUpload(api, {
      onUploadComplete: (file) => {
        console.log("Upload complete:", file);
      },
    });

  const handleUpload = async () => {
    if (!selectedFile) return;

    if (selectedFile.size > 100 * 1024 * 1024) {
      // Use chunked upload for files larger than 100MB
      await chunkedUpload(selectedFile, {
        parentFolderId: "folder-uuid",
      });
    } else {
      // Use standard upload for smaller files
      await upload(selectedFile, {
        parentFolderId: "folder-uuid",
      });
    }
  };

  return (
    <div>
      <input
        type="file"
        onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
      />

      <button onClick={handleUpload} disabled={!selectedFile || isUploading}>
        Upload
      </button>

      {isUploading && (
        <>
          <progress value={progress} max="100" />
          <button onClick={cancelUpload}>Cancel</button>
        </>
      )}
    </div>
  );
};
```

## API Documentation

For detailed API documentation, see:

- [File Management](docs/file-api.md)
- [Folder Management](docs/folder-api.md)
- [Permission Management](docs/permissions-api.md)
- [File Sharing](docs/sharing-api.md)
- [Chunked Upload](docs/chunked-upload.md)

## License

MIT
