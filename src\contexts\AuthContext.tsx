"use client";

import React, { createContext, useContext, ReactNode, useEffect } from "react";
import {
  AuthProvider,
  AuthProviderProps,
  useAuth as useOidcAuth,
} from "react-oidc-context";
import { oidcConfig } from "@/lib/oidcConfig";
import { Log } from "oidc-client-ts";

// Bật log chi tiết trong môi trường phát triển
if (process.env.NODE_ENV === "development") {
  Log.setLogger(console);
  Log.setLevel(Log.DEBUG);
}

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: any;
  login: () => void;
  logout: () => void;
  error: any | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProviderWrapper: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  return (
    <AuthProvider
      {...oidcConfig}
      onSigninCallback={() => {
        // Clear the URL of OIDC parameters after successful callback
        if (typeof window !== "undefined") {
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        }
      }}
    >
      <AuthContextProvider>{children}</AuthContextProvider>
    </AuthProvider>
  );
};

const AuthContextProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const auth = useOidcAuth();

  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      console.log("🔧 OIDC Configuration:", {
        ...oidcConfig,
        client_secret: "[HIDDEN]", // Ẩn thông tin nhạy cảm
      });
    }
  }, []);

  useEffect(() => {
    if (auth.error) {
      console.error("🚨 OIDC Error Details:", auth.error);

      // Thêm xử lý lỗi cụ thể
      if (auth.error.message?.includes("No state in response") ||
          auth.error.message?.includes("No matching state found")) {
        console.error("🚨 State missing in response. This might be due to:");
        console.error("- Browser cookie/storage issues");
        console.error("- Cross-origin problems");
        console.error("- Incorrect response_mode setting");
        console.error("- Multiple UserManager instances");

        // Thử xóa trạng thái cũ
        if (typeof window !== "undefined") {
          try {
            // Clear various OIDC storage keys
            const keysToRemove = [
              'oidc.user',
              'oidc.state',
              'auth_return_url',
              'login_attempted'
            ];

            keysToRemove.forEach(key => {
              localStorage.removeItem(key);
              sessionStorage.removeItem(key);
            });

            console.log("🧹 Cleared old OIDC state");
          } catch (e) {
            console.error("Failed to clear OIDC state:", e);
          }
        }
      }
    }
  }, [auth.error]);

  useEffect(() => {
    console.log("🔐 Auth State Changed:", {
      isAuthenticated: auth.isAuthenticated,
      isLoading: auth.isLoading,
      hasUser: !!auth.user,
      hasError: !!auth.error,
      errorType: auth.error ? auth.error.constructor.name : null,
      errorMessage: auth.error ? auth.error.message : null,
    });

    // Handle successful authentication
    if (auth.isAuthenticated && auth.user && !auth.isLoading) {
      console.log("✅ User authenticated successfully:", {
        name: auth.user.profile?.name,
        email: auth.user.profile?.email,
        sub: auth.user.profile?.sub,
      });
    }
  }, [auth.isAuthenticated, auth.isLoading, auth.user, auth.error]);

  const contextValue: AuthContextType = {
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    user: auth.user,
    login: () => {
      try {
        // Lưu lại URL hiện tại nếu chưa có returnUrl
        if (
          typeof window !== "undefined" &&
          !sessionStorage.getItem("auth_return_url")
        ) {
          const currentPath = window.location.pathname;
          if (currentPath !== "/auth/login" && currentPath !== "/auth/callback") {
            sessionStorage.setItem("auth_return_url", currentPath);
            console.log("🔐 Saved current path as return URL:", currentPath);
          }
        }

        // Thực hiện login
        console.log("🔑 Initiating signin redirect...");
        auth.signinRedirect({
          state: window.location.pathname // Pass current path as state
        });
      } catch (error) {
        console.error("🚨 Login initiation failed:", error);
      }
    },
    logout: () => {
      try {
        console.log("🚪 Initiating logout...");
        auth.signoutRedirect();
      } catch (error) {
        console.error("🚨 Logout failed:", error);
      }
    },
    error: auth.error ?? null,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProviderWrapper");
  }
  return context;
};
