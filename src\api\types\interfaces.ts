// File Management Interfaces
export interface FileDto {
  id: string;
  name: string;
  displayName?: string;
  fileSize: number;
  mimeType: string;
  filePath: string;
  hashMd5?: string;
  hashSha256?: string;
  storageProvider: StorageProvider;
  externalId?: string;
  parentFolderId?: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  version: number;
  permissions: Permission[];
  isShared: boolean;
  tags?: string[];
  description?: string;
  customMetadata?: Record<string, any>;
}

export enum StorageProvider {
  CloudflareR2 = 'CloudflareR2',
  GoogleDrive = 'GoogleDrive',
  Local = 'Local'
}

export enum Permission {
  Read = 'Read',
  Write = 'Write',
  Delete = 'Delete',
  Share = 'Share',
  Admin = 'Admin'
}

export interface UploadOptions {
  parentFolderId?: string;
  displayName?: string;
  description?: string;
  syncToGoogleDrive?: boolean;
  tags?: string[];
  overwriteExisting?: boolean;
  customMetadata?: Record<string, any>;
}

export interface MultiUploadOptions {
  parentFolderId?: string;
  syncToGoogleDrive?: boolean;
  failOnError?: boolean;
  tags?: string[];
}

export interface MultiUploadResponse {
  totalFiles: number;
  successfulUploads: number;
  failedUploads: number;
  uploadedFiles: FileDto[];
  errors: UploadError[];
  totalProcessingTime: string;
  totalSizeUploaded: number;
}

export interface UploadError {
  fileName: string;
  errorMessage: string;
  errorCode: string;
}

export interface FileUpdateData {
  displayName?: string;
  description?: string;
  parentFolderId?: string;
  tags?: string[];
}

export interface FileCopyOptions {
  targetFolderId: string;
  newName?: string;
  syncToGoogleDrive?: boolean;
}

export interface FileMoveOptions {
  targetFolderId: string;
}

export interface DownloadOptions {
  presigned?: boolean;
  expiration?: number;
}

export interface PresignedUrlResponse {
  url: string;
  expires: string;
}

// Folder Management Interfaces
export interface FolderDto {
  id: string;
  name: string;
  parentFolderId?: string;
  ownerId: string;
  path: string;
  level: number;
  createdAt: string;
  updatedAt: string;
  fileCount: number;
  subfolderCount: number;
  permissions: Permission[];
  description?: string;
}

export interface FolderCreateData {
  name: string;
  parentFolderId?: string;
  description?: string;
}

export interface FolderUpdateData {
  name?: string;
  description?: string;
  parentFolderId?: string;
}

export interface FolderContentsOptions {
  page?: number;
  pageSize?: number;
  sortBy?: SortField;
  sortDirection?: SortDirection;
}

export enum SortField {
  Name = 'Name',
  CreatedAt = 'CreatedAt',
  UpdatedAt = 'UpdatedAt',
  FileSize = 'FileSize'
}

export enum SortDirection {
  ASC = 'ASC',
  DESC = 'DESC'
}

export interface FolderContentsResponse {
  folders: FolderDto[];
  files: FileDto[];
  pagination: PaginationInfo;
}

export interface FolderDownloadOptions {
  includeSubfolders?: boolean;
  maxZipSize?: number;
}

// Permission Management Interfaces
export interface PermissionRequest {
  userId?: string;
  roleId?: string;
  permission: Permission;
  expiresAt?: string;
}

export interface PermissionDto {
  id: string;
  userId: string;
  userName: string;
  permission: Permission;
  grantedAt: string;
  expiresAt?: string;
  grantedBy: string;
}

// Sharing Interfaces
export enum ShareType {
  Public = 'Public',
  Password = 'Password',
  UserSpecific = 'UserSpecific'
}

export interface ShareOptions {
  shareType: ShareType;
  password?: string;
  expiresAt?: string;
  maxDownloads?: number;
}

export interface ShareDto {
  id: string;
  token: string;
  shareType: ShareType;
  shareUrl: string;
  expiresAt?: string;
  maxDownloads?: number;
  currentDownloads: number;
  isActive: boolean;
  createdAt: string;
}

export interface ShareAccessResponse {
  file: Partial<FileDto>;
  shareInfo: {
    shareType: ShareType;
    expiresAt?: string;
    remainingDownloads?: number;
  };
  downloadUrl: string;
}

// Sync Interfaces
export interface SyncOptions {
  fileId?: string;
  forceSync?: boolean;
}

export enum SyncStatus {
  Pending = 'Pending',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Failed = 'Failed',
  Cancelled = 'Cancelled'
}

export enum SyncDirection {
  ToCloud = 'ToCloud',
  FromCloud = 'FromCloud',
  Bidirectional = 'Bidirectional'
}

export interface SyncJobDto {
  id: string;
  fileId: string;
  provider: string;
  status: SyncStatus;
  progress: number;
  startedAt: string;
  completedAt?: string;
  errorMessage?: string;
  syncDirection: SyncDirection;
}

export interface SyncStatusResponse {
  syncJobs: SyncJobDto[];
  overallStatus: string;
}

export interface SyncTriggerResponse {
  jobId: string;
  status: string;
  message: string;
}

// Chunked Upload Interfaces
export interface ChunkedUploadOptions extends UploadOptions {
  chunkSize?: number;
  onProgress?: (progress: UploadProgress) => void;
  onChunkComplete?: (chunkNumber: number, totalChunks: number) => void;
  onError?: (error: Error, chunkNumber?: number) => void;
  maxRetries?: number;
  parallelUploads?: number;
}

export interface UploadProgress {
  totalChunks: number;
  uploadedChunks: number;
  progress: number;
  chunkNumber: number;
  uploadedBytes: number;
  totalBytes: number;
  estimatedTimeRemaining?: number;
  currentSpeed?: number;
}

export interface ChunkedUploadInitRequest {
  fileName: string;
  totalFileSize: number;
  contentType: string;
  fileHash?: string;
  parentFolderId?: string;
  displayName?: string;
  description?: string;
  syncToGoogleDrive?: boolean;
  tags?: string[];
}

export interface ChunkedUploadSession {
  sessionId: string;
  fileName: string;
  totalFileSize: number;
  chunkSize: number;
  totalChunks: number;
  expiresAt: string;
  uploadToken: string;
}

export interface ChunkUploadResult {
  chunkNumber: number;
  chunkSize: number;
  isLastChunk: boolean;
  chunkHash: string;
  remainingChunks: number;
  progressPercentage: number;
}

export interface CompleteChunkedUploadRequest {
  chunkHashes: string[];
  finalFileHash?: string;
}

export interface ChunkedUploadStatusResponse {
  session: ChunkedUploadSession;
  uploadedChunks: number[];
  progress: number;
  status: 'Active' | 'Expired' | 'Completed' | 'Failed';
}

// Pagination
export interface PaginationInfo {
  page: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
  nextPage?: string;
  previousPage?: string;
}

// API Error Response (RFC 7807 Problem Details)
export interface ApiErrorResponse {
  type: string;
  title: string;
  status: number;
  detail: string;
  instance: string;
  errors?: Record<string, string[]>;
  traceId: string;
}

// Rate Limiting
export interface RateLimitHeaders {
  limit: number;
  remaining: number;
  reset: number;
  retryAfter?: number;
}

// Validation Interfaces
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Business Logic Error Codes
export enum ErrorCode {
  // File Upload Errors
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  DUPLICATE_FILE_NAME = 'DUPLICATE_FILE_NAME',
  INVALID_PARENT_FOLDER = 'INVALID_PARENT_FOLDER',

  // Permission Errors
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  PERMISSION_ALREADY_EXISTS = 'PERMISSION_ALREADY_EXISTS',
  INVALID_PERMISSION_TYPE = 'INVALID_PERMISSION_TYPE',
  PERMISSION_EXPIRED = 'PERMISSION_EXPIRED',

  // Sharing Errors
  SHARE_LIMIT_EXCEEDED = 'SHARE_LIMIT_EXCEEDED',
  INVALID_SHARE_PASSWORD = 'INVALID_SHARE_PASSWORD',
  SHARE_EXPIRED = 'SHARE_EXPIRED',
  MAX_DOWNLOADS_REACHED = 'MAX_DOWNLOADS_REACHED',

  // Sync Errors
  SYNC_PROVIDER_UNAVAILABLE = 'SYNC_PROVIDER_UNAVAILABLE',
  SYNC_QUOTA_EXCEEDED = 'SYNC_QUOTA_EXCEEDED',
  SYNC_AUTHENTICATION_FAILED = 'SYNC_AUTHENTICATION_FAILED',

  // General Errors
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
}

// Configuration
export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enableLogging?: boolean;
  rateLimitRetry?: boolean;
}

// Health Check
export interface HealthCheckResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  services: {
    database: 'up' | 'down';
    storage: 'up' | 'down';
    sync: 'up' | 'down';
  };
}
