import { ApiClient } from '../core/apiClient';
import {
  FileDto,
  ChunkedUploadOptions,
  UploadProgress,
  ChunkedUploadInitRequest,
  ChunkedUploadSession,
  ChunkUploadResult
} from '../types/interfaces';

export class ChunkedUploadService {
  private client: ApiClient;
  private defaultChunkSize: number = 10 * 1024 * 1024; // 10MB default
  private maxConcurrentUploads: number = 3;
  
  constructor(apiClient: ApiClient) {
    this.client = apiClient;
  }
  
  /**
   * Upload a large file using chunked upload
   * @param file File to upload
   * @param options Upload options
   * @returns Information about the uploaded file
   */
  async uploadFile(file: File, options?: ChunkedUploadOptions): Promise<FileDto> {
    // Store chunk hashes for final verification
    const chunkHashes: string[] = [];
    const chunkSize = options?.chunkSize || this.defaultChunkSize;
    const totalChunks = Math.ceil(file.size / chunkSize);
    
    try {
      // Step 1: Initialize upload session
      const session = await this.initSession(file, options);
      
      // Step 2: Upload all chunks with parallel processing
      const uploadTasks = [];
      const uploadedChunks = new Set<number>();
      
      for (let i = 0; i < totalChunks; i++) {
        const chunkNumber = i + 1;
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        
        // Create blob for this chunk
        const chunk = file.slice(start, end);
        const chunkHash = await this.calculateSHA256(chunk);
        chunkHashes.push(chunkHash);
        
        // Create upload task
        uploadTasks.push(async () => {
          let retryCount = 0;
          const maxRetries = 3;
          
          while (retryCount <= maxRetries) {
            try {
              const result = await this.uploadChunk(session.sessionId, chunk, chunkNumber, chunkHash);
              uploadedChunks.add(chunkNumber);
              
              if (options?.onProgress) {
                options.onProgress({
                  totalChunks,
                  uploadedChunks: uploadedChunks.size,
                  progress: (uploadedChunks.size / totalChunks) * 100,
                  chunkNumber
                });
              }
              
              return result;
            } catch (error) {
              retryCount++;
              if (retryCount > maxRetries) throw error;
              
              // Exponential backoff
              const delay = Math.min(1000 * Math.pow(2, retryCount), 30000);
              await new Promise(resolve => setTimeout(resolve, delay));
            }
          }
        });
      }
      
      // Execute uploads with limited concurrency
      const results = await this.executeWithConcurrencyLimit(
        uploadTasks,
        this.maxConcurrentUploads
      );
      
      // Step 3: Complete the upload
      const finalFileHash = await this.calculateSHA256(file);
      return await this.completeUpload(session.sessionId, chunkHashes, finalFileHash);
      
    } catch (error) {
      console.error("Chunked upload failed", error);
      throw error;
    }
  }
  
  /**
   * Initialize a chunked upload session
   * @param file File to upload
   * @param options Upload options
   * @returns Session information
   */
  async initSession(file: File, options?: ChunkedUploadOptions): Promise<ChunkedUploadSession> {
    const fileHash = await this.calculateSHA256(file);
    
    const payload: ChunkedUploadInitRequest = {
      fileName: file.name,
      totalFileSize: file.size,
      contentType: file.type,
      fileHash,
      ...(options?.parentFolderId && { parentFolderId: options.parentFolderId }),
      ...(options?.displayName && { displayName: options.displayName }),
      ...(options?.description && { description: options.description }),
      ...(options?.syncToGoogleDrive !== undefined && { syncToGoogleDrive: options.syncToGoogleDrive }),
      ...(options?.tags && { tags: options.tags })
    };
    
    return this.client.post<ChunkedUploadSession>('/files/upload/chunked/init', payload);
  }
  
  /**
   * Upload a single chunk
   * @param sessionId ID of the upload session
   * @param chunk Chunk data as Blob
   * @param chunkNumber Sequence number of the chunk (1-based)
   * @param chunkHash SHA-256 hash of the chunk
   * @returns Result of the chunk upload
   */
  async uploadChunk(
    sessionId: string, 
    chunk: Blob, 
    chunkNumber: number,
    chunkHash: string
  ): Promise<ChunkUploadResult> {
    const formData = new FormData();
    formData.append('chunk', chunk);
    formData.append('chunkNumber', chunkNumber.toString());
    formData.append('chunkHash', chunkHash);
    
    return this.client.post<ChunkUploadResult>(
      `/files/upload/chunked/${sessionId}/chunk`,
      formData,
      { headers: { 'Content-Type': 'multipart/form-data' } }
    );
  }
  
  /**
   * Complete the chunked upload
   * @param sessionId ID of the upload session
   * @param chunkHashes Array of chunk hashes in order
   * @param finalFileHash Hash of the complete file
   * @returns Information about the uploaded file
   */
  async completeUpload(
    sessionId: string,
    chunkHashes: string[],
    finalFileHash: string
  ): Promise<FileDto> {
    return this.client.post<FileDto>(`/files/upload/chunked/${sessionId}/complete`, {
      chunkHashes,
      finalFileHash
    });
  }
  
  /**
   * Calculate SHA-256 hash of a file or blob
   * @param data File or blob to hash
   * @returns Hex string hash
   */
  private async calculateSHA256(data: File | Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsArrayBuffer(data);
      reader.onload = async (event) => {
        try {
          const buffer = event.target?.result as ArrayBuffer;
          const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
          const hashArray = Array.from(new Uint8Array(hashBuffer));
          const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
          resolve(hashHex);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = () => reject(reader.error);
    });
  }
  
  /**
   * Execute tasks with limited concurrency
   * @param tasks Array of task functions
   * @param concurrencyLimit Maximum number of concurrent tasks
   * @returns Array of task results
   */
  private async executeWithConcurrencyLimit<T>(
    tasks: (() => Promise<T>)[],
    concurrencyLimit: number
  ): Promise<T[]> {
    return new Promise((resolve) => {
      const results: T[] = new Array(tasks.length);
      let completed = 0;
      let nextTaskIndex = 0;
      let activeCount = 0;
      
      const startNext = () => {
        if (nextTaskIndex >= tasks.length) return;
        
        const taskIndex = nextTaskIndex++;
        activeCount++;
        
        tasks[taskIndex]().then(result => {
          results[taskIndex] = result;
          activeCount--;
          completed++;
          
          if (completed === tasks.length) {
            // All tasks completed
            resolve(results);
          } else {
            // Start next task
            startNext();
          }
        }).catch(error => {
          console.error(`Task ${taskIndex} failed:`, error);
          results[taskIndex] = null as any;
          activeCount--;
          completed++;
          
          if (completed === tasks.length) {
            // All tasks completed (some may have failed)
            resolve(results);
          } else {
            // Start next task
            startNext();
          }
        });
      };
      
      // Start initial batch of tasks
      for (let i = 0; i < Math.min(concurrencyLimit, tasks.length); i++) {
        startNext();
      }
    });
  }
  
  /**
   * Check status of an existing upload session
   * @param sessionId ID of the upload session
   * @returns Session information and progress
   */
  async getSessionStatus(sessionId: string): Promise<{
    session: ChunkedUploadSession;
    uploadedChunks: number[];
    progress: number;
  }> {
    return this.client.get<{
      session: ChunkedUploadSession;
      uploadedChunks: number[];
      progress: number;
    }>(`/files/upload/chunked/${sessionId}/status`);
  }
  
  /**
   * Resume an incomplete upload session
   * @param sessionId ID of the upload session
   * @param file Original file
   * @param options Upload options
   * @returns Information about the uploaded file
   */
  async resumeUpload(
    sessionId: string,
    file: File,
    options?: ChunkedUploadOptions
  ): Promise<FileDto> {
    try {
      // Get session status
      const { session, uploadedChunks } = await this.getSessionStatus(sessionId);
      const chunkSize = session.chunkSize;
      const totalChunks = session.totalChunks;
      
      // Store chunk hashes
      const chunkHashes: string[] = new Array(totalChunks);
      
      // Create upload tasks for missing chunks
      const uploadTasks = [];
      const currentUploadedChunks = new Set<number>(uploadedChunks);
      
      for (let i = 0; i < totalChunks; i++) {
        const chunkNumber = i + 1;
        
        // Skip already uploaded chunks
        if (currentUploadedChunks.has(chunkNumber)) {
          continue;
        }
        
        const start = i * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        
        // Create blob for this chunk
        const chunk = file.slice(start, end);
        const chunkHash = await this.calculateSHA256(chunk);
        chunkHashes[i] = chunkHash;
        
        // Create upload task
        uploadTasks.push(async () => {
          const result = await this.uploadChunk(session.sessionId, chunk, chunkNumber, chunkHash);
          currentUploadedChunks.add(chunkNumber);
          
          if (options?.onProgress) {
            options.onProgress({
              totalChunks,
              uploadedChunks: currentUploadedChunks.size,
              progress: (currentUploadedChunks.size / totalChunks) * 100,
              chunkNumber
            });
          }
          
          return result;
        });
      }
      
      // Execute uploads with limited concurrency
      if (uploadTasks.length > 0) {
        await this.executeWithConcurrencyLimit(
          uploadTasks,
          this.maxConcurrentUploads
        );
      }
      
      // Get hashes for already uploaded chunks
      for (const chunkNumber of uploadedChunks) {
        if (chunkNumber <= totalChunks) {
          const i = chunkNumber - 1;
          const start = i * chunkSize;
          const end = Math.min(start + chunkSize, file.size);
          
          const chunk = file.slice(start, end);
          const chunkHash = await this.calculateSHA256(chunk);
          chunkHashes[i] = chunkHash;
        }
      }
      
      // Complete the upload
      const finalFileHash = await this.calculateSHA256(file);
      return await this.completeUpload(session.sessionId, chunkHashes, finalFileHash);
      
    } catch (error) {
      console.error("Resume upload failed", error);
      throw error;
    }
  }
}

