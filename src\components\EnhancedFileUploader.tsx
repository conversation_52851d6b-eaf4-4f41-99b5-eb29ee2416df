import React, { useState, useCallback, useRef } from 'react';
import { useFileUpload } from '../api/hooks/useFileUpload';
import VeasyFileManagerAPI from '../api';
import { FileDto, UploadOptions, ChunkedUploadOptions } from '../api/types/interfaces';
import { getUserFriendlyErrorMessage, isValidationError, isApiError } from '../api';

interface EnhancedFileUploaderProps {
  apiClient: VeasyFileManagerAPI;
  parentFolderId?: string;
  onUploadComplete?: (files: FileDto[]) => void;
  onUploadError?: (error: Error) => void;
  maxFileSize?: number; // in bytes
  allowedFileTypes?: string[]; // MIME types or extensions
  enableChunkedUpload?: boolean;
  chunkSize?: number;
  maxConcurrentUploads?: number;
  className?: string;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
  result?: FileDto;
}

const EnhancedFileUploader: React.FC<EnhancedFileUploaderProps> = ({
  apiClient,
  parentFolderId,
  onUploadComplete,
  onUploadError,
  maxFileSize = 100 * 1024 * 1024, // 100MB default
  allowedFileTypes,
  enableChunkedUpload = true,
  chunkSize = 10 * 1024 * 1024, // 10MB chunks
  maxConcurrentUploads = 3,
  className = '',
}) => {
  const [uploadingFiles, setUploadingFiles] = useState<Map<string, UploadingFile>>(new Map());
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    upload,
    chunkedUpload,
    progress: globalProgress,
    isUploading: globalIsUploading,
    error: globalError,
    reset
  } = useFileUpload(apiClient, {
    onUploadComplete: (file) => {
      updateFileStatus(file.name, 'completed', 100, undefined, file);
    },
    onUploadError: (error) => {
      onUploadError?.(error);
    },
    autoChunkedThreshold: enableChunkedUpload ? maxFileSize / 2 : Infinity
  });

  // File validation
  const validateFile = (file: File): { isValid: boolean; error?: string } => {
    // Size validation
    if (file.size > maxFileSize) {
      return {
        isValid: false,
        error: `File "${file.name}" is too large. Maximum size is ${formatFileSize(maxFileSize)}`
      };
    }

    // Type validation
    if (allowedFileTypes && allowedFileTypes.length > 0) {
      const isAllowed = allowedFileTypes.some(type => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        return file.type === type;
      });

      if (!isAllowed) {
        return {
          isValid: false,
          error: `File type "${file.type}" is not allowed`
        };
      }
    }

    return { isValid: true };
  };

  const updateFileStatus = (
    fileName: string,
    status: UploadingFile['status'],
    progress: number,
    error?: string,
    result?: FileDto
  ) => {
    setUploadingFiles(prev => {
      const newMap = new Map(prev);
      const existing = newMap.get(fileName);
      if (existing) {
        newMap.set(fileName, {
          ...existing,
          status,
          progress,
          error,
          result
        });
      }
      return newMap;
    });
  };

  const uploadSingleFile = async (file: File): Promise<FileDto | null> => {
    const validation = validateFile(file);
    if (!validation.isValid) {
      updateFileStatus(file.name, 'error', 0, validation.error);
      throw new Error(validation.error);
    }

    updateFileStatus(file.name, 'uploading', 0);

    try {
      const uploadOptions: UploadOptions & ChunkedUploadOptions = {
        parentFolderId,
        displayName: file.name,
        syncToGoogleDrive: true,
        chunkSize,
        onProgress: (progressData) => {
          updateFileStatus(file.name, 'uploading', progressData.progress);
        }
      };

      let result: FileDto;

      // Decide whether to use chunked upload
      if (enableChunkedUpload && file.size > chunkSize * 2) {
        result = await chunkedUpload(file, uploadOptions);
      } else {
        result = await upload(file, uploadOptions);
      }

      updateFileStatus(file.name, 'completed', 100, undefined, result);
      return result;
    } catch (error) {
      const errorMessage = getUserFriendlyErrorMessage(error);
      updateFileStatus(file.name, 'error', 0, errorMessage);
      throw error;
    }
  };

  const handleFileSelection = async (files: FileList) => {
    const fileArray = Array.from(files);

    // Initialize all files in the uploading state
    setUploadingFiles(prev => {
      const newMap = new Map(prev);
      fileArray.forEach(file => {
        newMap.set(file.name, {
          file,
          progress: 0,
          status: 'pending'
        });
      });
      return newMap;
    });

    // Upload files with concurrency control
    const results: FileDto[] = [];
    const errors: Error[] = [];

    try {
      // Process files in batches to respect concurrency limit
      for (let i = 0; i < fileArray.length; i += maxConcurrentUploads) {
        const batch = fileArray.slice(i, i + maxConcurrentUploads);

        const batchPromises = batch.map(async (file) => {
          try {
            const result = await uploadSingleFile(file);
            if (result) results.push(result);
            return result;
          } catch (error) {
            errors.push(error as Error);
            return null;
          }
        });

        await Promise.allSettled(batchPromises);
      }

      // Call completion callback if any files were successful
      if (results.length > 0) {
        onUploadComplete?.(results);
      }

      // If there were errors, report them
      if (errors.length > 0 && results.length === 0) {
        onUploadError?.(new Error(`Failed to upload ${errors.length} file(s)`));
      }

    } catch (error) {
      onUploadError?.(error as Error);
    }
  };

  const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      handleFileSelection(files);
    }
    // Reset input to allow re-upload of same file
    event.target.value = '';
  };

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files && files.length > 0) {
      handleFileSelection(files);
    }
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  }, []);

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const clearCompleted = () => {
    setUploadingFiles(prev => {
      const newMap = new Map();
      prev.forEach((file, key) => {
        if (file.status !== 'completed') {
          newMap.set(key, file);
        }
      });
      return newMap;
    });
  };

  const uploadingFilesList = Array.from(uploadingFiles.values());
  const hasActiveUploads = uploadingFilesList.some(f => f.status === 'uploading');
  const hasCompletedUploads = uploadingFilesList.some(f => f.status === 'completed');

  return (
    <div className={`enhanced-file-uploader ${className}`}>
      {/* Upload Area */}
      <div
        className={`upload-area ${isDragOver ? 'drag-over' : ''} ${hasActiveUploads ? 'uploading' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileDialog}
        style={{
          border: '2px dashed #ccc',
          borderRadius: '8px',
          padding: '40px 20px',
          textAlign: 'center',
          cursor: 'pointer',
          backgroundColor: isDragOver ? '#f0f8ff' : '#fafafa',
          transition: 'all 0.3s ease'
        }}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          onChange={handleFileInputChange}
          style={{ display: 'none' }}
          accept={allowedFileTypes?.join(',')}
        />

        <div style={{ fontSize: '48px', marginBottom: '16px', color: '#666' }}>
          📁
        </div>

        <h3 style={{ margin: '0 0 8px 0', color: '#333' }}>
          {hasActiveUploads ? 'Uploading Files...' : 'Drop files here or click to upload'}
        </h3>

        <p style={{ margin: '0', color: '#666' }}>
          Maximum file size: {formatFileSize(maxFileSize)}
          {allowedFileTypes && (
            <><br/>Allowed types: {allowedFileTypes.join(', ')}</>
          )}
        </p>

        {hasActiveUploads && (
          <div style={{ marginTop: '16px' }}>
            <div style={{
              width: '100%',
              height: '8px',
              backgroundColor: '#e0e0e0',
              borderRadius: '4px',
              overflow: 'hidden'
            }}>
              <div
                style={{
                  width: `${globalProgress}%`,
                  height: '100%',
                  backgroundColor: '#4CAF50',
                  transition: 'width 0.3s ease'
                }}
              />
            </div>
            <p style={{ margin: '8px 0 0 0', fontSize: '14px', color: '#666' }}>
              Overall Progress: {Math.round(globalProgress)}%
            </p>
          </div>
        )}
      </div>

      {/* File List */}
      {uploadingFilesList.length > 0 && (
        <div style={{ marginTop: '20px' }}>
          <div style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            marginBottom: '12px'
          }}>
            <h4 style={{ margin: 0 }}>Files ({uploadingFilesList.length})</h4>
            {hasCompletedUploads && (
              <button
                onClick={clearCompleted}
                style={{
                  padding: '4px 8px',
                  fontSize: '12px',
                  border: '1px solid #ccc',
                  borderRadius: '4px',
                  backgroundColor: 'white',
                  cursor: 'pointer'
                }}
              >
                Clear Completed
              </button>
            )}
          </div>

          <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
            {uploadingFilesList.map((fileUpload) => (
              <div
                key={fileUpload.file.name}
                style={{
                  padding: '12px',
                  border: '1px solid #e0e0e0',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  backgroundColor: 'white'
                }}
              >
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <div style={{ flex: 1 }}>
                    <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                      {fileUpload.file.name}
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {formatFileSize(fileUpload.file.size)}
                    </div>
                  </div>

                  <div style={{ textAlign: 'right' }}>
                    <div style={{
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      color: 'white',
                      backgroundColor:
                        fileUpload.status === 'completed' ? '#4CAF50' :
                        fileUpload.status === 'error' ? '#f44336' :
                        fileUpload.status === 'uploading' ? '#2196F3' : '#9E9E9E'
                    }}>
                      {fileUpload.status.toUpperCase()}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                {(fileUpload.status === 'uploading' || fileUpload.status === 'completed') && (
                  <div style={{ marginTop: '8px' }}>
                    <div style={{
                      width: '100%',
                      height: '4px',
                      backgroundColor: '#e0e0e0',
                      borderRadius: '2px',
                      overflow: 'hidden'
                    }}>
                      <div
                        style={{
                          width: `${fileUpload.progress}%`,
                          height: '100%',
                          backgroundColor: fileUpload.status === 'completed' ? '#4CAF50' : '#2196F3',
                          transition: 'width 0.3s ease'
                        }}
                      />
                    </div>
                    <div style={{ fontSize: '12px', color: '#666', marginTop: '2px' }}>
                      {Math.round(fileUpload.progress)}%
                    </div>
                  </div>
                )}

                {/* Error Message */}
                {fileUpload.error && (
                  <div style={{
                    marginTop: '8px',
                    padding: '8px',
                    backgroundColor: '#ffebee',
                    border: '1px solid #ffcdd2',
                    borderRadius: '4px',
                    color: '#c62828',
                    fontSize: '12px'
                  }}>
                    {fileUpload.error}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Global Error */}
      {globalError && (
        <div style={{
          marginTop: '16px',
          padding: '12px',
          backgroundColor: '#ffebee',
          border: '1px solid #ffcdd2',
          borderRadius: '4px',
          color: '#c62828'
        }}>
          <strong>Upload Error:</strong> {getUserFriendlyErrorMessage(globalError)}
        </div>
      )}
    </div>
  );
};

export default EnhancedFileUploader;
