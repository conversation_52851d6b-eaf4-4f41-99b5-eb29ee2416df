"use client";

import React, { useState, useEffect } from "react";
import { useModal } from "@/components/providers/ModalProvider";
import {
  FolderIcon,
  DocumentIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  CloudArrowUpIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ArrowDownTrayIcon,
  ShareIcon,
  ChevronRightIcon,
  HomeIcon,
  Bars3Icon,
  Squares2X2Icon,
} from "@heroicons/react/24/outline";

// Types
interface FileItem {
  id: string;
  name: string;
  type: "file" | "folder";
  size?: number;
  parentId?: string;
  createdAt: Date;
  updatedAt: Date;
  uploadedBy: string;
  url?: string;
  mimeType?: string;
}

interface BreadcrumbItem {
  id: string;
  name: string;
  path: string;
}

export default function FilesPage() {
  // State management
  const [files, setFiles] = useState<FileItem[]>([]);
  const [currentFolderId, setCurrentFolderId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");
  const [sortBy, setSortBy] = useState<"name" | "date" | "size" | "type">(
    "name"
  );
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [filterType, setFilterType] = useState<"all" | "files" | "folders">(
    "all"
  );
  const [isUploading, setIsUploading] = useState(false);

  const { openDeleteModal, openCreateFolderModal, openUploadFilesModal } =
    useModal();
  // Sample data for demonstration
  useEffect(() => {
    if (files.length === 0) {
      const sampleFiles: FileItem[] = [
        {
          id: "folder-1",
          name: "Phòng ban A",
          type: "folder",
          parentId: undefined,
          createdAt: new Date("2024-01-15"),
          updatedAt: new Date("2024-01-15"),
          uploadedBy: "<EMAIL>",
        },
        {
          id: "folder-2",
          name: "Phòng ban B",
          type: "folder",
          parentId: undefined,
          createdAt: new Date("2024-01-16"),
          updatedAt: new Date("2024-01-16"),
          uploadedBy: "<EMAIL>",
        },
        {
          id: "folder-3",
          name: "Phòng ban C",
          type: "folder",
          parentId: undefined,
          createdAt: new Date("2024-01-17"),
          updatedAt: new Date("2024-01-17"),
          uploadedBy: "<EMAIL>",
        },
        {
          id: "folder-4",
          name: "Phòng ban D",
          type: "folder",
          parentId: undefined,
          createdAt: new Date("2024-01-18"),
          updatedAt: new Date("2024-01-18"),
          uploadedBy: "<EMAIL>",
        },
        {
          id: "file-1",
          name: "Báo cáo tháng 1.pdf",
          type: "file",
          size: 2048576, // 2MB
          parentId: "folder-1",
          createdAt: new Date("2024-01-20"),
          updatedAt: new Date("2024-01-20"),
          uploadedBy: "<EMAIL>",
          url: "/sample.pdf",
          mimeType: "application/pdf",
        },
        {
          id: "file-2",
          name: "Hóa đơn Q1 2024.pdf",
          type: "file",
          size: 1024000, // 1MB
          parentId: "folder-1",
          createdAt: new Date("2024-01-18"),
          updatedAt: new Date("2024-01-18"),
          uploadedBy: "<EMAIL>",
          url: "/invoice.pdf",
          mimeType: "application/pdf",
        },
        {
          id: "file-3",
          name: "Kế hoạch 2024.pdf",
          type: "file",
          size: 3072000, // 3MB
          parentId: undefined,
          createdAt: new Date("2024-01-22"),
          updatedAt: new Date("2024-01-22"),
          uploadedBy: "<EMAIL>",
          url: "/plan.pdf",
          mimeType: "application/pdf",
        },
      ];
      setFiles(sampleFiles);
    }
  }, [files.length]);

  // Utility functions
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
    }).format(new Date(date));
  };

  // Get current folder items
  const getCurrentItems = () => {
    return files.filter((item) => item.parentId === currentFolderId);
  };

  // Filter and search items
  const getFilteredItems = () => {
    let items = getCurrentItems();

    // Apply search filter
    if (searchTerm) {
      items = items.filter((item) =>
        item.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    if (filterType !== "all") {
      items = items.filter((item) =>
        filterType === "files" ? item.type === "file" : item.type === "folder"
      );
    }

    // Apply sorting
    items.sort((a, b) => {
      let comparison = 0;
      switch (sortBy) {
        case "name":
          comparison = a.name.localeCompare(b.name);
          break;
        case "date":
          comparison = a.updatedAt.getTime() - b.updatedAt.getTime();
          break;
        case "size":
          comparison = (a.size || 0) - (b.size || 0);
          break;
        case "type":
          comparison = a.type.localeCompare(b.type);
          break;
      }
      return sortOrder === "asc" ? comparison : -comparison;
    });

    return items;
  };

  // Get breadcrumb path
  const getBreadcrumbs = (): BreadcrumbItem[] => {
    const breadcrumbs: BreadcrumbItem[] = [
      { id: "root", name: "Trang chủ", path: "/" },
    ];

    if (currentFolderId) {
      const folder = files.find((f) => f.id === currentFolderId);
      if (folder) {
        breadcrumbs.push({
          id: folder.id,
          name: folder.name,
          path: `/${folder.id}`,
        });
      }
    }

    return breadcrumbs;
  };

  // Event handlers
  const handleFolderClick = (folderId: string) => {
    setCurrentFolderId(folderId);
    setSelectedItems([]);
  };

  const handleBreadcrumbClick = (folderId: string) => {
    setCurrentFolderId(folderId === "root" ? null : folderId);
    setSelectedItems([]);
  };

  const handleItemSelect = (itemId: string, isSelected: boolean) => {
    if (isSelected) {
      setSelectedItems([...selectedItems, itemId]);
    } else {
      setSelectedItems(selectedItems.filter((id) => id !== itemId));
    }
  };

  const handleSelectAll = () => {
    const currentItems = getFilteredItems();
    if (selectedItems.length === currentItems.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(currentItems.map((item) => item.id));
    }
  };
  const handleCreateFolder = () => {
    openCreateFolderModal((name: string) => {
      const newFolder: FileItem = {
        id: `folder-${Date.now()}`,
        name: name.trim(),
        type: "folder",
        parentId: currentFolderId || undefined,
        createdAt: new Date(),
        updatedAt: new Date(),
        uploadedBy: "<EMAIL>",
      };
      setFiles([...files, newFolder]);
    });
  };

  const handleUploadFiles = () => {
    openUploadFilesModal((selectedFiles: File[]) => {
      setIsUploading(true);
      // Simulate upload
      setTimeout(() => {
        selectedFiles.forEach((file) => {
          const newFile: FileItem = {
            id: `file-${Date.now()}-${Math.random()}`,
            name: file.name,
            type: "file",
            size: file.size,
            parentId: currentFolderId || undefined,
            createdAt: new Date(),
            updatedAt: new Date(),
            uploadedBy: "<EMAIL>",
            mimeType: file.type,
          };
          setFiles((prev) => [...prev, newFile]);
        });
        setIsUploading(false);
      }, 2000);
    });
  };

  const handleDeleteItem = (itemId: string) => {
    openDeleteModal({
      title: "Xác nhận xóa",
      message: "Bạn có chắc chắn muốn xóa mục này không?",
      itemName: files.find((f) => f.id === itemId)?.name || "",
      itemType: "tệp/thư mục",
      onConfirm: () => {
        setFiles(files.filter((f) => f.id !== itemId));
        setSelectedItems(selectedItems.filter((id) => id !== itemId));
      },
    });
  };

  const handleRenameItem = (itemId: string) => {
    const item = files.find((f) => f.id === itemId);
    if (item) {
      const newName = prompt("Nhập tên mới:", item.name);
      if (newName && newName.trim() && newName !== item.name) {
        setFiles(
          files.map((f) =>
            f.id === itemId
              ? { ...f, name: newName.trim(), updatedAt: new Date() }
              : f
          )
        );
      }
    }
  };

  const handleDownloadItem = (itemId: string) => {
    const item = files.find((f) => f.id === itemId);
    if (item && item.type === "file") {
      // Simulate download
      console.log("Downloading:", item.name);
      alert(`Đang tải xuống: ${item.name}`);
    }
  };

  const handleViewItem = (itemId: string) => {
    const item = files.find((f) => f.id === itemId);
    if (item && item.type === "file") {
      // Navigate to viewer
      window.open(`/viewer?file=${itemId}`, "_blank");
    }
  };

  const filteredItems = getFilteredItems();
  const breadcrumbs = getBreadcrumbs();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <FolderIcon className="h-8 w-8 text-indigo-600 mr-3" />
                <h1 className="text-2xl font-bold text-gray-900">
                  Quản lý folder
                </h1>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleCreateFolder}
                className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg text-sm font-medium hover:bg-indigo-700 transition-colors"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Thêm mới
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-6 py-6">
        {/* Breadcrumb */}
        <nav className="flex mb-6" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-1 md:space-x-3">
            {breadcrumbs.map((crumb, index) => (
              <li key={crumb.id} className="inline-flex items-center">
                {index > 0 && (
                  <ChevronRightIcon className="w-4 h-4 text-gray-400 mx-1" />
                )}
                <button
                  onClick={() => handleBreadcrumbClick(crumb.id)}
                  className={`inline-flex items-center text-sm font-medium ${
                    index === breadcrumbs.length - 1
                      ? "text-gray-500 cursor-default"
                      : "text-blue-600 hover:text-blue-800"
                  }`}
                >
                  {index === 0 && <HomeIcon className="w-4 h-4 mr-1" />}
                  {crumb.name}
                </button>
              </li>
            ))}
          </ol>
        </nav>

        {/* Search and Controls */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
          <div className="p-4">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
              {/* Search */}
              <div className="flex-1 max-w-lg">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Tìm kiếm theo Tên Folder/ File"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  />
                </div>
              </div>

              {/* Controls */}
              <div className="flex items-center space-x-4">
                {/* Filter */}
                <div className="flex items-center space-x-2">
                  <label className="text-sm text-gray-700">Loại:</label>{" "}
                  <select
                    value={filterType}
                    onChange={(e) =>
                      setFilterType(
                        e.target.value as "all" | "files" | "folders"
                      )
                    }
                    className="border border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                  >
                    <option value="all">Tất cả</option>
                    <option value="folders">Thư mục</option>
                    <option value="files">Tệp tin</option>
                  </select>
                </div>

                {/* Upload Button */}
                <button
                  onClick={handleUploadFiles}
                  disabled={isUploading}
                  className="inline-flex items-center px-3 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50 transition-colors"
                >
                  <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                  {isUploading ? "Đang tải..." : "Tải lên"}
                </button>

                {/* View Mode */}
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 ${
                      viewMode === "list"
                        ? "bg-indigo-100 text-indigo-600"
                        : "text-gray-400 hover:text-gray-600"
                    }`}
                  >
                    <Bars3Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 ${
                      viewMode === "grid"
                        ? "bg-indigo-100 text-indigo-600"
                        : "text-gray-400 hover:text-gray-600"
                    }`}
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                </div>

                {/* Search Button */}
                <button className="inline-flex items-center px-4 py-2 bg-teal-500 text-white rounded-md text-sm font-medium hover:bg-teal-600 transition-colors">
                  <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                  Tìm kiếm
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* File Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          {/* Table Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <input
                  type="checkbox"
                  checked={
                    filteredItems.length > 0 &&
                    selectedItems.length === filteredItems.length
                  }
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">
                  {selectedItems.length > 0
                    ? `Đã chọn ${selectedItems.length} mục`
                    : `${filteredItems.length} mục`}
                </span>
              </div>
              {selectedItems.length > 0 && (
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() =>
                      selectedItems.forEach((id) => handleDeleteItem(id))
                    }
                    className="inline-flex items-center px-3 py-1 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200 transition-colors"
                  >
                    <TrashIcon className="h-4 w-4 mr-1" />
                    Xóa
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Table Content */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex items-center space-x-1">
                      <span>Loại</span>
                    </div>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      onClick={() => {
                        if (sortBy === "name") {
                          setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                        } else {
                          setSortBy("name");
                          setSortOrder("asc");
                        }
                      }}
                      className="flex items-center space-x-1 hover:text-gray-700"
                    >
                      <span>Tên</span>
                      {sortBy === "name" && (
                        <span className="text-indigo-600">
                          {sortOrder === "asc" ? "↑" : "↓"}
                        </span>
                      )}
                    </button>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Người upload
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      onClick={() => {
                        if (sortBy === "date") {
                          setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                        } else {
                          setSortBy("date");
                          setSortOrder("desc");
                        }
                      }}
                      className="flex items-center space-x-1 hover:text-gray-700"
                    >
                      <span>Sửa đổi lần cuối</span>
                      {sortBy === "date" && (
                        <span className="text-indigo-600">
                          {sortOrder === "asc" ? "↑" : "↓"}
                        </span>
                      )}
                    </button>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <button
                      onClick={() => {
                        if (sortBy === "size") {
                          setSortOrder(sortOrder === "asc" ? "desc" : "asc");
                        } else {
                          setSortBy("size");
                          setSortOrder("desc");
                        }
                      }}
                      className="flex items-center space-x-1 hover:text-gray-700"
                    >
                      <span>Kích cỡ tập</span>
                      {sortBy === "size" && (
                        <span className="text-indigo-600">
                          {sortOrder === "asc" ? "↑" : "↓"}
                        </span>
                      )}
                    </button>
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredItems.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-12 text-center">
                      <div className="flex flex-col items-center">
                        <FolderIcon className="h-12 w-12 text-gray-400 mb-4" />
                        <h3 className="text-lg font-medium text-gray-900 mb-2">
                          Không có tệp nào
                        </h3>
                        <p className="text-gray-500 mb-4">
                          {searchTerm
                            ? "Không tìm thấy tệp nào phù hợp với tìm kiếm của bạn"
                            : "Thư mục này trống. Hãy tải lên tệp hoặc tạo thư mục mới."}
                        </p>
                        <div className="flex space-x-3">
                          <button
                            onClick={handleCreateFolder}
                            className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md text-sm font-medium hover:bg-indigo-700"
                          >
                            <PlusIcon className="h-4 w-4 mr-2" />
                            Tạo thư mục
                          </button>
                          <button
                            onClick={handleUploadFiles}
                            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
                          >
                            <CloudArrowUpIcon className="h-4 w-4 mr-2" />
                            Tải lên tệp
                          </button>
                        </div>
                      </div>
                    </td>
                  </tr>
                ) : (
                  filteredItems.map((item) => (
                    <tr
                      key={item.id}
                      className={`hover:bg-gray-50 ${
                        selectedItems.includes(item.id) ? "bg-blue-50" : ""
                      }`}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(item.id)}
                            onChange={(e) =>
                              handleItemSelect(item.id, e.target.checked)
                            }
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mr-3"
                          />
                          {item.type === "folder" ? (
                            <FolderIcon className="h-6 w-6 text-blue-500" />
                          ) : (
                            <DocumentIcon className="h-6 w-6 text-red-500" />
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <button
                          onClick={() =>
                            item.type === "folder"
                              ? handleFolderClick(item.id)
                              : handleViewItem(item.id)
                          }
                          className="text-sm font-medium text-gray-900 hover:text-indigo-600 text-left"
                        >
                          {item.name}
                        </button>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.uploadedBy}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(item.updatedAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {item.type === "file" && item.size
                          ? formatFileSize(item.size)
                          : "--"}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex items-center space-x-2">
                          {item.type === "file" && (
                            <>
                              <button
                                onClick={() => handleViewItem(item.id)}
                                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
                                title="Xem"
                              >
                                <EyeIcon className="h-4 w-4" />
                              </button>
                              <button
                                onClick={() => handleDownloadItem(item.id)}
                                className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                                title="Tải xuống"
                              >
                                <ArrowDownTrayIcon className="h-4 w-4" />
                              </button>
                            </>
                          )}
                          <button
                            onClick={() => handleRenameItem(item.id)}
                            className="p-1 text-gray-400 hover:text-yellow-600 transition-colors"
                            title="Đổi tên"
                          >
                            <PencilIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => {}}
                            className="p-1 text-gray-400 hover:text-purple-600 transition-colors"
                            title="Chia sẻ"
                          >
                            <ShareIcon className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteItem(item.id)}
                            className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                            title="Xóa"
                          >
                            <TrashIcon className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Stats */}
        {filteredItems.length > 0 && (
          <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Thống kê thư mục
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {
                    filteredItems.filter((item) => item.type === "folder")
                      .length
                  }
                </div>
                <div className="text-sm text-gray-600">Thư mục</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {filteredItems.filter((item) => item.type === "file").length}
                </div>
                <div className="text-sm text-gray-600">Tệp tin</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {Math.round(
                    filteredItems
                      .filter((item) => item.type === "file")
                      .reduce((total, file) => total + (file.size || 0), 0) /
                      1024 /
                      1024
                  )}
                  MB
                </div>
                <div className="text-sm text-gray-600">Tổng dung lượng</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {selectedItems.length}
                </div>
                <div className="text-sm text-gray-600">Đã chọn</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
