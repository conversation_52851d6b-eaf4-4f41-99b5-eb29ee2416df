"use client";
import React from "react";
import { useAuth } from "@/contexts/AuthContext";

const AuthButton: React.FC = () => {
  const { isAuthenticated, isLoading, user, userProfile, isLoadingProfile, login, logout, error } = useAuth();

  if (isLoading) {
    return (
      <button
        className="px-4 py-2 rounded bg-gray-300 text-gray-700 cursor-not-allowed"
        disabled
        aria-busy="true"
        aria-label="Đang kiểm tra xác thực"
      >
        Đang kiểm tra...
      </button>
    );
  }

  if (error) {
    return (
      <div className="text-red-600 text-sm mt-2" role="alert">
        <PERSON><PERSON> xảy ra lỗi xác thực: {error.message}
      </div>
    );
  }

  if (isAuthenticated && user) {
    // Use userProfile if available, fallback to OIDC user profile
    const displayName = userProfile?.name || userProfile?.firstName || user.profile?.name || user.profile?.email;
    const displayEmail = userProfile?.email || user.profile?.email;

    return (
      <div className="flex items-center gap-2">
        {isLoadingProfile && (
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        )}
        <span
          className="text-sm text-gray-700"
          aria-label={`Xin chào ${displayName || displayEmail}`}
        >
          Xin chào, {displayName || displayEmail}
        </span>
        <button
          className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-400"
          onClick={logout}
          aria-label="Đăng xuất"
          tabIndex={0}
          onKeyDown={(e) => {
            if (e.key === "Enter" || e.key === " ") logout();
          }}
        >
          Đăng xuất
        </button>
      </div>
    );
  }

  return (
    <button
      className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400"
      onClick={login}
      aria-label="Đăng nhập"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === "Enter" || e.key === " ") login();
      }}
    >
      Đăng nhập
    </button>
  );
};

export default AuthButton;
