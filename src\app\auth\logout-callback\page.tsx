"use client";

import React, { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { UserManager, UserManagerSettings } from "oidc-client-ts";
import { oidcConfig } from "@/lib/oidcConfig";
import { DocumentIcon } from "@heroicons/react/24/outline";

export default function LogoutCallbackPage() {
  const router = useRouter();
  const [status, setStatus] = useState<"processing" | "success" | "error">(
    "processing"
  );
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleLogoutCallback = async () => {
      try {
        const userManager = new UserManager(oidcConfig as UserManagerSettings);

        // Handle the logout callback
        await userManager.signoutRedirectCallback();

        setStatus("success");
        // Redirect to home page after successful logout
        setTimeout(() => {
          router.push("/");
        }, 1500);
      } catch (err) {
        console.error("Logout callback error:", err);
        setStatus("error");
        setError(err instanceof Error ? err.message : "Logout process failed");
      }
    };

    handleLogoutCallback();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center">
            <DocumentIcon className="h-12 w-12 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            PDF OCR Dashboard
          </h2>
        </div>

        <div className="mt-8 space-y-6">
          {status === "processing" && (
            <div className="text-center">
              <div className="mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
              <p className="text-gray-600">Completing logout process...</p>
            </div>
          )}

          {status === "success" && (
            <div className="text-center">
              <div className="mb-4">
                <div className="rounded-full h-8 w-8 bg-green-100 flex items-center justify-center mx-auto">
                  <svg
                    className="h-5 w-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-gray-600">
                You have been successfully logged out! Redirecting...
              </p>
            </div>
          )}

          {status === "error" && (
            <div className="text-center">
              <div className="mb-4">
                <div className="rounded-full h-8 w-8 bg-red-100 flex items-center justify-center mx-auto">
                  <svg
                    className="h-5 w-5 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-red-600 mb-4">Logout process failed</p>
              {error && <p className="text-red-500 text-sm mb-4">{error}</p>}
              <button
                onClick={() => router.push("/")}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Return to home
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
