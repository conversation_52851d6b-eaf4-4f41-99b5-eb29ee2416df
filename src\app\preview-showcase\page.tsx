"use client";

import React from "react";
import {
  EyeIcon,
  DocumentIcon,
  SparklesIcon,
  CursorArrowRaysIcon,
  MagnifyingGlassIcon,
  ArrowsPointingOutIcon,
} from "@heroicons/react/24/outline";

export default function PreviewShowcasePage() {
  const demos = [
    {
      id: "auto",
      title: "Auto Detach Preview",
      description:
        "Preview PDF files from auto detach processing with blue-purple gradient",
      color: "from-blue-600 to-purple-600",
      link: "/processing/auto",
    },
    {
      id: "manual",
      title: "Manual Detach Preview",
      description:
        "Preview PDF files from manual detach processing with green-blue gradient",
      color: "from-green-600 to-blue-600",
      link: "/processing/manual",
    },
    {
      id: "test",
      title: "Test Preview",
      description: "Standalone PDF preview demo with purple-pink gradient",
      color: "from-purple-600 to-pink-600",
      link: "/test-preview",
    },
  ];

  const features = [
    {
      icon: <MagnifyingGlassIcon className="h-6 w-6" />,
      title: "Zoom Controls",
      description: "Zoom in/out from 50% to 200% with smooth controls",
    },
    {
      icon: <CursorArrowRaysIcon className="h-6 w-6" />,
      title: "Page Navigation",
      description: "Navigate through PDF pages with arrow controls",
    },
    {
      icon: <ArrowsPointingOutIcon className="h-6 w-6" />,
      title: "Fullscreen Mode",
      description: "Toggle fullscreen for immersive PDF viewing",
    },
    {
      icon: <SparklesIcon className="h-6 w-6" />,
      title: "Modern Design",
      description: "Beautiful gradient headers and smooth animations",
    },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
            <DocumentIcon className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Enhanced PDF Preview
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Experience our redesigned PDF preview with modern controls,
            fullscreen mode, and beautiful gradient themes across all pages.
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg mb-4 text-white">
                {feature.icon}
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm">{feature.description}</p>
            </div>
          ))}
        </div>

        {/* Demo Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {demos.map((demo) => (
            <div
              key={demo.id}
              className="bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1"
            >
              {/* Card Header */}
              <div className={`bg-gradient-to-r ${demo.color} px-6 py-8`}>
                <div className="flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-full mx-auto mb-4">
                  <EyeIcon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white text-center">
                  {demo.title}
                </h3>
              </div>

              {/* Card Body */}
              <div className="p-6">
                <p className="text-gray-600 mb-6 text-center">
                  {demo.description}
                </p>

                {/* Preview Features */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center text-sm text-gray-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    Zoom controls (50% - 200%)
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    Page navigation
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    Fullscreen toggle
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                    Download functionality
                  </div>
                </div>

                {/* Action Button */}
                <a
                  href={demo.link}
                  className={`w-full inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r ${demo.color} text-white rounded-lg hover:opacity-90 transition-opacity font-medium`}
                >
                  <EyeIcon className="h-5 w-5 mr-2" />
                  Try Preview
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Instructions */}
        <div className="mt-16 bg-white rounded-2xl p-8 shadow-lg">
          <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            How to Test PDF Preview
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 text-blue-600 rounded-full mb-4 font-bold text-lg">
                1
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Choose a Page
              </h3>
              <p className="text-gray-600 text-sm">
                Visit Auto Detach, Manual Detach, or Test Preview page
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-green-100 text-green-600 rounded-full mb-4 font-bold text-lg">
                2
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Click Eye Icon
              </h3>
              <p className="text-gray-600 text-sm">
                Click the eye icon next to any PDF file to open preview
              </p>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-purple-100 text-purple-600 rounded-full mb-4 font-bold text-lg">
                3
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">
                Explore Features
              </h3>
              <p className="text-gray-600 text-sm">
                Use zoom, navigation, fullscreen, and download controls
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
