# File Management API - Quản lý File

## Tổng quan
API quản lý file cung cấp đầy đủ tính năng upload, download, move, copy, delete, chia sẻ và quản lý quyền truy cập file.

**Base URL:** `/api/v1/files`

---

## 1. Upload File

### `POST /api/v1/files/upload`

Upload file mới lên hệ thống với hỗ trợ chunked upload cho file lớn.

#### Request Headers

| Header | Bắt buộc | Mô tả |
|--------|----------|-------|
| `Content-Type` | Có | `multipart/form-data` |
| `Authorization` | Có | Bearer token |

#### Request Body (multipart/form-data)

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `file` | File | Có | File cần upload |
| `fileName` | string | Có | Tên file (1-255 ký tự) |
| `parentFolderId` | UUID | Không | ID folder chứa file |
| `replaceExisting` | boolean | Không | Ghi đè file trùng tên |
| `chunkIndex` | integer | Không | Index của chunk (chunked upload) |
| `totalChunks` | integer | Không | Tổng số chunks |
| `uploadSessionId` | UUID | Không | ID session cho chunked upload |

#### Response Body

**Upload thành công:**
```json
{
  "success": true,
  "message": "File uploaded successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "document.pdf",
    "displayName": "Báo cáo tháng 3.pdf",
    "filePath": "/uploads/user123/2023/03/document.pdf",
    "fileSize": 2048576,
    "formattedSize": "2 MB",
    "mimeType": "application/pdf",
    "hashMd5": "abc123def456...",
    "hashSha256": "789xyz012...",
    "storageProvider": "R2",
    "parentFolderId": "456e7890-e89b-12d3-a456-************",
    "ownerId": "user123-456-789",
    "createdAt": "2023-03-22T10:30:00Z",
    "updatedAt": "2023-03-22T10:30:00Z",
    "version": 1,
    "downloadUrl": "/api/v1/files/123e4567-e89b-12d3-a456-************/download"
  }
}
```

**Chunked upload in progress:**
```json
{
  "success": true,
  "message": "Chunk 2/5 uploaded successfully",
  "data": {
    "uploadSessionId": "session123-456",
    "chunkIndex": 2,
    "totalChunks": 5,
    "uploadProgress": 40.0,
    "nextChunkExpected": 3
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Upload thành công
- `202 Accepted`: Chunk upload thành công

**❌ Lỗi:**
- `400 Bad Request`: File không hợp lệ, tên file trùng, kích thước quá lớn
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền upload vào folder
- `413 Payload Too Large`: File vượt quá giới hạn (5GB)
- `415 Unsupported Media Type`: Loại file không được phép
- `507 Insufficient Storage`: Không đủ dung lượng

#### Ví dụ sử dụng

```bash
# Upload file đơn giản
POST /api/v1/files/upload
Content-Type: multipart/form-data

--boundary123
Content-Disposition: form-data; name="file"; filename="document.pdf"
Content-Type: application/pdf

[file content]
--boundary123
Content-Disposition: form-data; name="fileName"

Báo cáo tháng 3.pdf
--boundary123--

# Chunked upload
POST /api/v1/files/upload
Content-Type: multipart/form-data

file=[chunk1]&fileName=largefile.zip&chunkIndex=0&totalChunks=10&uploadSessionId=session123
```

---

## 2. Download File

### `GET /api/v1/files/{fileId}/download`

Download file từ hệ thống.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `fileId` | UUID | Có | ID của file |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `inline` | boolean | Không | Hiển thị inline thay vì download |
| `shareToken` | string | Không | Token chia sẻ (cho file public) |

#### Response Headers

| Header | Mô tả |
|--------|-------|
| `Content-Type` | MIME type của file |
| `Content-Length` | Kích thước file |
| `Content-Disposition` | attachment; filename="filename.ext" |
| `Cache-Control` | Caching policy |
| `ETag` | File version identifier |

#### Response Body

Binary content của file

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Download thành công
- `206 Partial Content`: Range request thành công
- `304 Not Modified`: File không thay đổi (cache hit)

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền download
- `404 Not Found`: File không tồn tại
- `410 Gone`: File đã bị xóa
- `416 Range Not Satisfiable`: Range request không hợp lệ

#### Ví dụ sử dụng

```bash
# Download thông thường
GET /api/v1/files/123e4567-e89b-12d3-a456-************/download

# Xem inline (images, PDFs)
GET /api/v1/files/123e4567-e89b-12d3-a456-************/download?inline=true

# Download với share token
GET /api/v1/files/123e4567-e89b-12d3-a456-************/download?shareToken=abc123

# Range request (resume download)
GET /api/v1/files/123e4567-e89b-12d3-a456-************/download
Range: bytes=1024000-2047999
```

---

## 3. Lấy thông tin file

### `GET /api/v1/files/{fileId}`

Lấy thông tin chi tiết của file.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `fileId` | UUID | Có | ID của file |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "document.pdf",
    "displayName": "Báo cáo tháng 3.pdf",
    "filePath": "/uploads/user123/2023/03/document.pdf",
    "fileSize": 2048576,
    "formattedSize": "2 MB",
    "mimeType": "application/pdf",
    "hashMd5": "abc123def456...",
    "hashSha256": "789xyz012...",
    "storageProvider": "R2",
    "externalId": "r2-object-123",
    "parentFolderId": "456e7890-e89b-12d3-a456-************",
    "ownerId": "user123-456-789",
    "createdAt": "2023-03-22T10:30:00Z",
    "updatedAt": "2023-03-22T10:30:00Z",
    "deletedAt": null,
    "isDeleted": false,
    "version": 1,
    "downloadUrl": "/api/v1/files/123e4567-e89b-12d3-a456-************/download",
    "thumbnailUrl": "/api/v1/files/123e4567-e89b-12d3-a456-************/thumbnail",
    "permissions": [
      {
        "id": "perm123",
        "userId": "user456",
        "userEmail": "<EMAIL>",
        "permissionType": "Read",
        "grantedAt": "2023-03-22T11:00:00Z"
      }
    ],
    "shares": [
      {
        "id": "share123",
        "shareToken": "abc123xyz",
        "shareType": "Public",
        "expiresAt": "2023-04-22T10:30:00Z",
        "isActive": true
      }
    ]
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Lấy thông tin thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền xem file
- `404 Not Found`: File không tồn tại

---

## 4. Danh sách files của user

### `GET /api/v1/files/my-files`

Lấy danh sách files của người dùng hiện tại với phân trang và lọc.

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mặc định | Mô tả |
|---------|------|----------|----------|-------|
| `page` | integer | Không | 1 | Số trang |
| `pageSize` | integer | Không | 20 | Số items/trang (1-100) |
| `parentFolderId` | UUID | Không | - | ID folder chứa files |
| `searchTerm` | string | Không | - | Tìm kiếm theo tên file |
| `mimeType` | string | Không | - | Lọc theo MIME type |
| `minSize` | integer | Không | - | Kích thước tối thiểu (bytes) |
| `maxSize` | integer | Không | - | Kích thước tối đa (bytes) |
| `createdAfter` | datetime | Không | - | Tạo sau ngày |
| `createdBefore` | datetime | Không | - | Tạo trước ngày |
| `sortBy` | string | Không | `createdAt` | Sắp xếp: `name`, `size`, `createdAt` |
| `sortOrder` | string | Không | `desc` | Thứ tự: `asc`, `desc` |

#### Response Body

```json
{
  "success": true,
  "message": "Success",
  "data": {
    "files": [
      {
        "id": "123e4567-e89b-12d3-a456-************",
        "name": "document.pdf",
        "displayName": "Báo cáo tháng 3.pdf",
        "fileSize": 2048576,
        "formattedSize": "2 MB",
        "mimeType": "application/pdf",
        "createdAt": "2023-03-22T10:30:00Z",
        "updatedAt": "2023-03-22T10:30:00Z",
        "downloadUrl": "/api/v1/files/123e4567-e89b-12d3-a456-************/download",
        "thumbnailUrl": "/api/v1/files/123e4567-e89b-12d3-a456-************/thumbnail"
      }
    ],
    "totalCount": 157,
    "page": 1,
    "pageSize": 20,
    "totalPages": 8
  }
}
```

#### Ví dụ sử dụng

```bash
# Lấy tất cả files
GET /api/v1/files/my-files

# Files trong folder cụ thể
GET /api/v1/files/my-files?parentFolderId=folder123

# Tìm kiếm PDF files
GET /api/v1/files/my-files?searchTerm=report&mimeType=application/pdf

# Files lớn hơn 10MB
GET /api/v1/files/my-files?minSize=10485760

# Sắp xếp theo tên
GET /api/v1/files/my-files?sortBy=name&sortOrder=asc
```

---

## 5. Di chuyển file

### `PUT /api/v1/files/{fileId}/move`

Di chuyển file sang folder khác.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `fileId` | UUID | Có | ID của file |

#### Request Body

```json
{
  "newParentFolderId": "456e7890-e89b-12d3-a456-************",
  "newFileName": "renamed-document.pdf"
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `newParentFolderId` | UUID | Có | ID folder đích (null = root) |
| `newFileName` | string | Không | Tên mới cho file |

#### Response Body

```json
{
  "success": true,
  "message": "File moved successfully",
  "data": {
    "id": "123e4567-e89b-12d3-a456-************",
    "name": "renamed-document.pdf",
    "parentFolderId": "456e7890-e89b-12d3-a456-************",
    "updatedAt": "2023-03-22T11:00:00Z",
    "version": 2
  }
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Di chuyển thành công

**❌ Lỗi:**
- `400 Bad Request`: Folder đích không hợp lệ, tên file trùng
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền di chuyển file hoặc không có quyền ghi vào folder đích
- `404 Not Found`: File hoặc folder đích không tồn tại
- `409 Conflict`: File cùng tên đã tồn tại trong folder đích

---

## 6. Sao chép file

### `POST /api/v1/files/{fileId}/copy`

Tạo bản sao của file.

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `fileId` | UUID | Có | ID của file gốc |

#### Request Body

```json
{
  "targetFolderId": "456e7890-e89b-12d3-a456-************",
  "newFileName": "Copy of document.pdf",
  "copyPermissions": false
}
```

| Trường | Kiểu | Bắt buộc | Mô tả |
|--------|------|----------|-------|
| `targetFolderId` | UUID | Không | ID folder đích (null = cùng folder) |
| `newFileName` | string | Không | Tên file copy (mặc định: "Copy of [original]") |
| `copyPermissions` | boolean | Không | Sao chép quyền truy cập |

#### Response Body

```json
{
  "success": true,
  "message": "File copied successfully",
  "data": {
    "id": "new-file-id-123",
    "name": "Copy of document.pdf",
    "parentFolderId": "456e7890-e89b-12d3-a456-************",
    "fileSize": 2048576,
    "createdAt": "2023-03-22T11:30:00Z"
  }
}
```

---

## 7. Xóa file

### `DELETE /api/v1/files/{fileId}`

Xóa file (soft delete - chuyển vào recycle bin hoặc xóa vĩnh viễn).

#### Path Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `fileId` | UUID | Có | ID của file |

#### Query Parameters

| Tham số | Kiểu | Bắt buộc | Mô tả |
|---------|------|----------|-------|
| `permanent` | boolean | Không | true = xóa vĩnh viễn, false = soft delete |

#### Response Body

**Soft delete:**
```json
{
  "success": true,
  "message": "File moved to recycle bin successfully",
  "data": true
}
```

**Permanent delete:**
```json
{
  "success": true,
  "message": "File permanently deleted successfully (storage file preserved)",
  "data": true
}
```

#### Các trường hợp hay gặp

**✅ Thành công:**
- `200 OK`: Xóa thành công

**❌ Lỗi:**
- `401 Unauthorized`: Chưa đăng nhập
- `403 Forbidden`: Không có quyền xóa file
- `404 Not Found`: File không tồn tại
- `409 Conflict`: File đang được sử dụng

---

## Workflow sử dụng

### 1. Upload và quản lý file
```
1. POST /files/upload → Upload file
2. GET /files/my-files → Xem danh sách
3. PUT /files/{id} → Cập nhật metadata
4. PUT /files/{id}/move → Di chuyển
```

### 2. Download file
```
1. GET /files/{id}/download → Download trực tiếp
2. GET /files/{id}/download?shareToken=xxx → Download qua share link
```

## Error Codes tổng hợp

| Code | HTTP Status | Mô tả |
|------|-------------|-------|
| `FILE_NOT_FOUND` | 404 | File không tồn tại |
| `PERMISSION_DENIED` | 403 | Không có quyền truy cập |
| `FILE_TOO_LARGE` | 413 | File vượt quá giới hạn |
| `INVALID_FILE_TYPE` | 415 | Loại file không được phép |
| `DUPLICATE_NAME` | 409 | Tên file đã tồn tại |
| `INSUFFICIENT_STORAGE` | 507 | Không đủ dung lượng |
| `UPLOAD_SESSION_EXPIRED` | 410 | Session upload hết hạn |
| `CHUNK_OUT_OF_ORDER` | 400 | Chunk upload sai thứ tự |

## File Size Limits

- **Single file:** 5GB
- **Chunked upload:** Unlimited (theo quota user)
- **Chunk size:** 1MB - 100MB
- **Session timeout:** 24 hours

## Supported File Types

- **Documents:** PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX
- **Images:** JPG, PNG, GIF, BMP, TIFF, WEBP
- **Videos:** MP4, AVI, MOV, WMV, FLV
- **Audio:** MP3, WAV, FLAC, AAC
- **Archives:** ZIP, RAR, 7Z, TAR, GZ
- **Others:** TXT, CSV, JSON, XML, etc.
