"use client";

import React, { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { DocumentIcon } from "@heroicons/react/24/outline";

export default function AuthCallbackPage() {
  const router = useRouter();
  const { isAuthenticated, isLoading, error, user } = useAuth();

  useEffect(() => {
    // Log current state for debugging
    console.log("🔄 Callback Page State:", {
      isAuthenticated,
      isLoading,
      hasUser: !!user,
      hasError: !!error,
    });

    // Wait for authentication to complete
    if (!isLoading && isAuthenticated && user) {
      console.log("✅ Authentication successful, redirecting...");

      // Get return URL from session storage
      let returnUrl = "/";
      try {
        const storedUrl = sessionStorage.getItem("auth_return_url");
        if (storedUrl) {
          returnUrl = storedUrl;
          sessionStorage.removeItem("auth_return_url");
          console.log("🔗 Redirecting to stored URL:", returnUrl);
        }
      } catch (e) {
        console.error("Failed to get return URL from session storage:", e);
      }

      // Redirect after a brief delay to show success message
      setTimeout(() => {
        router.push(returnUrl);
      }, 1500);
    } else if (!isLoading && error) {
      console.error("❌ Authentication failed:", error);
    }
  }, [isAuthenticated, isLoading, error, user, router]);

  const handleRetry = () => {
    router.push("/auth/login");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-12 flex items-center justify-center">
            <DocumentIcon className="h-12 w-12 text-blue-600" />
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            PDF OCR Dashboard
          </h2>
        </div>

        <div className="mt-8 space-y-6">
          {isLoading && (
            <div className="text-center">
              <div className="mb-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              </div>
              <p className="text-gray-600">Processing authentication...</p>
            </div>
          )}

          {!isLoading && isAuthenticated && user && (
            <div className="text-center">
              <div className="mb-4">
                <div className="rounded-full h-8 w-8 bg-green-100 flex items-center justify-center mx-auto">
                  <svg
                    className="h-5 w-5 text-green-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-gray-600">
                Authentication successful! Redirecting...
              </p>
            </div>
          )}

          {!isLoading && error && (
            <div className="text-center">
              <div className="mb-4">
                <div className="rounded-full h-8 w-8 bg-red-100 flex items-center justify-center mx-auto">
                  <svg
                    className="h-5 w-5 text-red-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </div>
              </div>
              <p className="text-red-600 mb-4">Authentication failed</p>
              <p className="text-red-500 text-sm mb-4">
                {error?.message || "Unknown authentication error"}
              </p>
              <button
                onClick={handleRetry}
                className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Try again
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
