import { useEffect, useState } from "react";

import { useAuth } from "@/contexts/AuthContext";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { UnauthorizedMessage } from "@/components/ui/UnauthorizedMessage";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: string[];
  requiredPermissions?: string[];
  fallback?: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  fallback,
}) => {
  const { isAuthenticated, isLoading, user, login, error } = useAuth();
  const [isChecking, setIsChecking] = useState(true);
  console.log("ProtectedRoute render", { isAuthenticated, isLoading, user });
  useEffect(() => {
    const checkAccess = async () => {
      // Đợi auth load xong
      if (isLoading) return;
      if (error) return;

      // <PERSON><PERSON><PERSON> chưa login, gọi login và return
      if (!isAuthenticated) {
        await login();
        return;
      }

      // Check role/permission
      if (requiredRoles.length > 0 && user) {
        const hasRequiredRole = requiredRoles.some((role) =>
          user.roles?.includes(role)
        );
        if (!hasRequiredRole) {
          setIsChecking(false);
          return;
        }
      }
      if (requiredPermissions.length > 0 && user) {
        const hasRequiredPermission = requiredPermissions.some((permission) =>
          user.permissions?.includes(permission)
        );
        if (!hasRequiredPermission) {
          setIsChecking(false);
          return;
        }
      }

      setIsChecking(false);
    };

    checkAccess();
  }, [
    isAuthenticated,
    isLoading,
    user,
    login,
    requiredRoles,
    requiredPermissions,
    error,
  ]);

  // Nếu đang loading hoặc đang check, show spinner
  if (isLoading || isChecking) {
    return fallback || <LoadingSpinner />;
  }

  // Nếu có lỗi xác thực
  if (error) {
    return <UnauthorizedMessage message={`Authentication error: ${error}`} />;
  }

  // Nếu không xác thực (đã gọi login, không render gì nữa)
  if (!isAuthenticated) {
    return null;
  }

  // Nếu không đủ quyền
  if (requiredRoles.length > 0 && user) {
    const hasRequiredRole = requiredRoles.some((role) =>
      user.roles?.includes(role)
    );
    if (!hasRequiredRole) {
      return (
        <UnauthorizedMessage
          message={`This page requires one of the following roles: ${requiredRoles.join(
            ", "
          )}`}
        />
      );
    }
  }
  if (requiredPermissions.length > 0 && user) {
    const hasRequiredPermission = requiredPermissions.some((permission) =>
      user.permissions?.includes(permission)
    );
    if (!hasRequiredPermission) {
      return (
        <UnauthorizedMessage
          message={`This page requires one of the following permissions: ${requiredPermissions.join(
            ", "
          )}`}
        />
      );
    }
  }

  // Đủ quyền, render children
  return <>{children}</>;
};
